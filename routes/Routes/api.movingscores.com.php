<?php

/**
 * Created by PhpStorm.
 * User: ped<PERSON><PERSON>de
 * Date: 13/09/18
 * Time: 14:37
 */

Route::group(['middleware' => ['web', 'cors']], function () {
    # Auth
    Route::post('/auth/login', 'MovingScores\AuthController@login');
    Route::post('/auth/register', 'MovingScores\AuthController@register');
    Route::post('/auth/check', 'MovingScores\AuthController@check');
    Route::post('/auth/recover-password', 'MovingScores\AuthController@recoverPassword');
    Route::post('/auth/reset-password', 'MovingScores\AuthController@resetPassword');

    Route::get('/header', 'MovingScores\HeaderController@index');
    Route::get('/search', 'MovingScores\SearchController@index');

    # Football Controllers
    Route::prefix('football')->group(function () {
        Route::get('/main-leagues', 'MovingScores\Football\MainLeagues@index');
        Route::get('/main-teams', 'MovingScores\Football\MainTeams@index');
        Route::get('/fixtures', 'MovingScores\Football\Fixtures@index');
        Route::get('/fixtures/{id}', 'MovingScores\Football\Fixtures@show');
        Route::get('/fixtures/{id}/events', 'MovingScores\Football\Fixtures@events');
        Route::get('/fixtures/{id}/stats', 'MovingScores\Football\Fixtures@stats');
        Route::get('/standings', 'MovingScores\Football\Standings@index');
        Route::get('/latest-news', 'MovingScores\Football\News@latest');        
        Route::get('/highlights', 'MovingScores\Football\Highlights@index');

        Route::get('/leagues/{id}', 'MovingScores\Football\Leagues@show');
    });
});
