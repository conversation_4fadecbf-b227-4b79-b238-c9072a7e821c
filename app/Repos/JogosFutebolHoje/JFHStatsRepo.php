<?php

namespace App\Repos\JogosFutebolHoje;

use App\Libs\Filters\InFilter;
use App\Libs\HandleCache\HandleCache;
use App\Libs\Http\Pagination;
use App\Libs\Repo\EloquentRepoResultDeferrer;
use App\Models\Brand;
use App\Models\Client;
use App\Models\EnterpriseTeamUser;
use App\Models\EnterpriseUser;
use App\Models\JFHFixture;
use App\Models\JFHFixtureEvent;
use App\Models\JFHFixtureStat;
use App\Models\JFHLeague;
use App\Models\JFHPlayer;
use App\Models\JFHStanding;
use App\Models\JFHTeam;
use App\Models\JFHTeamStat;
use App\Models\KCategory;
use App\Models\Message;
use App\Models\SiteUser;
use App\Models\Ticker;
use App\Models\User;
use App\Models\UserTeam;
use App\Repos\Repo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class JFHStatsRepo extends Repo
{
    use EloquentRepoResultDeferrer;

    public function getBestAttack($currentSeason, $league_id) {
        return JFHStanding::query()
            ->with(['Team'])
            ->where('league_id', $league_id)
            ->where('season_id', $currentSeason->id)
            ->groupBy('team_id')
            ->orderByRaw('(home_goals_for + away_goals_for) desc')
            ->first();
    }

    public function getBestDefense($currentSeason, $league_id) {
        return JFHStanding::query()
            ->with(['Team'])
            ->where('league_id', $league_id)
            ->where('season_id', $currentSeason->id)
            ->groupBy('team_id')
            ->orderByRaw('(home_goals_against + away_goals_against) asc')
            ->first();
    }

    public function getTotalWinsLosesAndDraws($currentSeason, $league_id) {
        $stats = [
            'games' => 0,
            'wins' => 0,
            'loses' => 0,
            'draws' => 0
        ];
        $query = DB::select("select team_id, sum(home_games + away_games) games, (home_win + away_win) wins, (home_lose + away_lose) loses, (home_draw + away_draw) draws from jfh_standings where league_id = ? and season_id = ? group by team_id;", [$league_id, $currentSeason->id]);

        foreach($query as $stat) {
            foreach(array_keys($stats) as $key) {
                $stats[$key] += (int) $stat->$key;
            }
        }

        return $stats;
    }

    public function getGoals($currentSeason, $league_id) {
        $stats = [
            0 => 0,
            1 => 0,
            2 => 0,
            3 => 0,
            4 => 0,
            5 => 0,
            6 => 0,
            7 => 0,
            '8+' => 0
        ];

        $fixtures = JFHFixture::query()
            ->where('league_id', $league_id)
            ->where('season_id', $currentSeason->id)
            ->select(['home_goals', 'away_goals'])
            ->where('status', 'FT')
            ->get();

        foreach($fixtures as $fixture) {
            $goals = (int) $fixture->home_goals + (int) $fixture->away_goals;            

            switch($goals) {
                case 0: 
                case 1: 
                case 2: 
                case 3: 
                case 4: 
                case 5: 
                case 6: 
                case 7: {
                    $stats[$goals] += 1;
                    break;
                }
                default: {
                    $stats['8+'] += 1;
                    break;
                }
            }
        }

        return $stats;
    }

    public function getGoalsAvg($currentSeason, $league_id) {
        return DB::selectOne("select (sum(home_goals + away_goals) / count(*)) num, count(*) games from jfh_fixtures where league_id = ? and season_id = ? and status = 'FT'", [$league_id, $currentSeason->id]);
    }

    public function getCards($currentSeason, $league_id) {
        $stats = [
            'Yellow Card' => 0,
            'Red Card' => 0
        ];

        $fixtureIds = JFHFixture::query()
            ->where('league_id', $league_id)
            ->where('season_id', $currentSeason->id)
            ->where('status', 'FT')
            ->select(['id'])
            ->get()
            ->map(function($item) {
                return $item->id;
            });

        $fixtureEvents = JFHFixtureEvent::query()
            ->whereIn('fixture_id', $fixtureIds)
            ->where('type', 'Card')
            ->get();

        foreach($fixtureEvents as $fixtureEvent) {
            $stats[$fixtureEvent->detail] += 1;
        }

        return $stats;
    }

    public function getScoredGoals($teamsIds, $season_id) {
        $fixtureIds = JFHFixture::query()
            ->where(function($q) use ($teamsIds) {
                $q->whereIn('home_team_id', $teamsIds)
                ->orWhereIn('away_team_id', $teamsIds);
            })
            ->where(function($q) use ($teamsIds) {
                $q->whereIn('away_team_id', $teamsIds)
                ->orWhereIn('home_team_id', $teamsIds);
            })            
            ->where('season_id', $season_id)
            ->select(['id'])
            ->get()
            ->map(function($item) {
                return $item->id;
            })
            ->toArray();

        return JFHFixtureEvent::query()
            ->where('type', 'Goal')
            ->whereIn('fixture_id', $fixtureIds)
            ->whereIn('team_id', $teamsIds)
            ->select(['elapsed', 'extra', 'team_id'])
            ->get();
    }

    public function getTeamOrLeague($param)
    {
        $exp = explode('-', $param);
        $id = $exp[0];

        $slug = '';
        foreach($exp as $index => $piece) {
            if ($index > 0) {
                $slug .= '-' . $piece;
            }
        }

        $slug = substr($slug, 1);

        $query = "
            SELECT type FROM (
                SELECT 
                    COUNT(*) num,
                    'team' type
                FROM 
                    jfh_teams 
                WHERE 
                    id = {$id} AND 
                    slug = '{$slug}'
                UNION ALL
                SELECT 
                    COUNT(*) num,
                    'league' type
                FROM 
                    jfh_leagues 
                WHERE 
                    id = {$id} AND 
                    slug = '{$slug}'
            ) x WHERE num = 1
        ";

        return DB::selectOne($query);
    }

    public function getGlobalLeagueStats(int $leagueId, int $seasonId)
    {
        $query = "
            SELECT 
                SUM(1) total_games,
                SUM(IF(status = 'FT', 1,0)) played_games,
                SUM(home_goals + away_goals) goals,
                SUM(IF(home_goals > away_goals,1,0)) wins_home,
                SUM(IF(home_goals < away_goals,1,0)) wins_away,
                SUM(IF(home_goals = away_goals AND status = 'FT',1,0)) draws,
                SUM(IF((home_goals + away_goals) > 3 AND status = 'FT',1,0)) games_with_more_then_35_goals,
                SUM(IF((home_goals + away_goals) <= 3 AND status = 'FT',1,0)) games_with_less_then_35_goals,
                SUM(IF(home_goals > 0 AND away_goals > 0 AND status = 'FT',1,0)) games_btts,
                (SELECT 
                    GROUP_CONCAT(CONCAT(result,'|', num)) 
                FROM (
                    SELECT CONCAT(home_goals,'-',away_goals) result, COUNT(*) num FROM jfh_fixtures WHERE league_id = {$leagueId} AND season_id = {$seasonId} AND status = 'FT' GROUP BY CONCAT(home_goals,'-',away_goals) ORDER BY COUNT(*) DESC LIMIT 3
                ) x) result
            FROM jfh_fixtures 
            WHERE 
                league_id = {$leagueId} AND 
                season_id = {$seasonId};";

        return DB::selectOne($query);
    }

    public function getBestAttackTeams(int $seasonId, int $leagueId) 
    {
        return JFHStanding::query()
            ->with(['Team'])
            ->where('league_id', $leagueId)
            ->where('season_id', $seasonId)
            ->groupBy('team_id')
            ->orderByRaw('(home_goals_for + away_goals_for) desc')
            ->get();
    }

    public function getBestDefenseTeams(int $seasonId, int $leagueId) 
    {
        return JFHStanding::query()
            ->with(['Team'])
            ->where('league_id', $leagueId)
            ->where('season_id', $seasonId)
            ->groupBy('team_id')
            ->orderByRaw('(home_goals_against + away_goals_against) asc')
            ->get();
    }

    public function getMoreWinsTeams(int $seasonId, int $leagueId) 
    {
        return JFHStanding::query()
            ->with(['Team'])
            ->where('league_id', $leagueId)
            ->where('season_id', $seasonId)
            ->groupBy('team_id')
            ->orderByRaw('(home_win + away_win) desc')
            ->get();
    }

    public function getMoreDrawsTeams(int $seasonId, int $leagueId) 
    {
        return JFHStanding::query()
            ->with(['Team'])
            ->where('league_id', $leagueId)
            ->where('season_id', $seasonId)
            ->groupBy('team_id')
            ->orderByRaw('(home_draw + away_draw) desc')
            ->get();
    }

    public function getMoreLossesTeams(int $seasonId, int $leagueId) 
    {
        return JFHStanding::query()
            ->with(['Team'])
            ->where('league_id', $leagueId)
            ->where('season_id', $seasonId)
            ->groupBy('team_id')
            ->orderByRaw('(home_lose + away_lose) desc')
            ->get();
    }

    public function getCardsTeams(int $seasonId, int $leagueId, string $detail)
    {
        $query = "
            SELECT 
                t.id,
                t.name, 
                t.filename,
                COUNT(*) num
            FROM jfh_fixtures_events fe
            JOIN jfh_teams t ON t.id = fe.team_id
            WHERE 
                fe.fixture_id IN (SELECT id FROM jfh_fixtures WHERE season_id = {$seasonId} AND league_id = {$leagueId}) AND 
                fe.type = 'Card' AND
                fe.detail = '{$detail}'
            GROUP BY fe.team_id
            ORDER BY num DESC;
        ";

        return DB::select($query);
    }

    public function getMostScoresTop10(int $seasonId, int $leagueId)
    {
        $query = "
            SELECT 
                fe.player_id, 
                fe.team_id, 
                p.name player_name,
                p.filename player_filename,
                t.name team_name,
                t.filename team_filename,
                COUNT(*) num 
            FROM jfh_fixtures_events fe
            JOIN jfh_players p on p.id = fe.player_id
            JOIN jfh_teams t on t.id = fe.team_id
            WHERE
                fe.type = 'Goal' AND 
                fe.detail NOT IN ('Missed Penalty') AND 
                fe.fixture_id IN (
                    SELECT id FROM jfh_fixtures WHERE league_id = {$leagueId} AND season_id = {$seasonId}
                )
            GROUP BY fe.player_id
            ORDER BY num DESC
            LIMIT 10;
        ";

        return DB::select($query);
    }

    public function getMostAssistsTop10(int $seasonId, int $leagueId)
    {
        $query = "
            SELECT 
                fe.assist_id, 
                (GROUP_CONCAT(fe.team_id) - 0) team_id, 
                p.name player_name,
                p.filename player_filename,
                (GROUP_CONCAT(DISTINCT t.name))  team_name, 
                t.filename team_filename,
                COUNT(*) num 
            FROM jfh_fixtures_events fe
            JOIN jfh_players p on p.id = fe.assist_id
            JOIN jfh_teams t on t.id = fe.team_id
            WHERE
                fe.type = 'Goal' AND 
                fe.detail NOT IN ('Missed Penalty') AND 
                fe.fixture_id IN (
                    SELECT id FROM jfh_fixtures WHERE league_id = {$leagueId} AND season_id = {$seasonId}
                )
            GROUP BY fe.assist_id
            ORDER BY num DESC
            LIMIT 10;
        ";

        return DB::select($query);
    }

    public function getMoreInfluencePlayersTop10(int $seasonId, int $leagueId)
    {
        $query = "
            SELECT 
                player_id, 
                p.name, 
                p.filename, 
                (GROUP_CONCAT(z.team_id) - 0) team_id, 
                (GROUP_CONCAT(t.name)) team_name, 
                (GROUP_CONCAT(t.filename)) team_filename, 
                SUM(num1 + num2) num 
            FROM (
                SELECT 
                    fe.player_id, 
                    (GROUP_CONCAT(fe.team_id) - 0) team_id,
                    COUNT(*) num1,
                    0 num2 
                FROM jfh_fixtures_events fe
                JOIN jfh_players p on p.id = fe.player_id
                JOIN jfh_teams t on t.id = fe.team_id
                WHERE
                    fe.type = 'Goal' AND 
                    fe.detail NOT IN ('Missed Penalty') AND 
                    fe.fixture_id IN (
                        SELECT id FROM jfh_fixtures WHERE league_id = {$leagueId} AND season_id = {$seasonId}
                    )
                    GROUP BY fe.player_id
                UNION ALL
                SELECT 
                    fe.assist_id player_id, 
                    (GROUP_CONCAT(fe.team_id) - 0) team_id,
                    0 num1,
                    COUNT(*) num2 
                FROM jfh_fixtures_events fe
                JOIN jfh_players p on p.id = fe.assist_id
                JOIN jfh_teams t on t.id = fe.team_id
                WHERE
                    fe.type = 'Goal' AND 
                    fe.detail NOT IN ('Missed Penalty') AND 
                    fe.fixture_id IN (
                        SELECT id FROM jfh_fixtures WHERE league_id = {$leagueId} AND season_id = {$seasonId}
                    )
                    GROUP BY fe.assist_id
                ) z 
            JOIN jfh_teams t on t.id = z.team_id
            JOIN jfh_players p on p.id = z.player_id
            GROUP BY z.player_id 
            ORDER BY SUM(num1 + num2) DESC 
            LIMIT 10;
        ";

        return DB::select($query);
    }

    public function getMostScoresAvgTop10(int $seasonId, int $leagueId)
    {
        $query = "
            SELECT 
                x.goals,
                x.games,
                ROUND(COALESCE((x.goals / x.games), 0),2) avg_goals_per_game,
                p.name player_name,
                p.filename player_filename,
                t.name team_name,
                t.filename team_filename
            FROM (
                SELECT 
                    fp.*,
                    SUM((select COUNT(*) FROM jfh_fixtures_events WHERE fixture_id = fp.fixture_id AND type = 'Goal' AND detail NOT IN ('Missed Penalty') AND player_id = fp.player_id)) goals,
                    COUNT(distinct fp.fixture_id) games
                FROM jfh_fixtures_players fp
                WHERE
                    fp.fixture_id IN (
                        SELECT id FROM jfh_fixtures WHERE league_id = {$leagueId} AND season_id = {$seasonId} AND status = 'FT'
                    )
                GROUP bY fp.player_id
            ) x 
            JOIN jfh_teams t ON t.id = x.team_id
            JOIN jfh_players p ON p.id = x.player_id
            ORDER BY goals DESC
            LIMIT 10;
        ";

        return DB::select($query);
    }

    public function getLeagueStats(int $seasonId, int $leagueId)
    {
        return JFHTeamStat::query()
            ->with(['Team'])
            ->where('season_id', $seasonId)
            ->where('league_id', $leagueId)
            ->get();
    }

    public function getTeamStats(int $seasonId, int $teamId)
    {
        return JFHTeamStat::query()
            ->with(['League'])
            ->where('season_id', $seasonId)
            ->where('team_id', $teamId)
            ->get();
    }

    public function getAll(int $fixtureId) 
    {    
        return JFHFixtureStat::query()
            ->where('fixture_id', $fixtureId)
            ->get();
    }

    public function getProLive(int $fixtureId) 
    {
        $fixture = JFHFixture::query()
            ->with(['HomeTeam', 'AwayTeam'])
            ->where('id', $fixtureId)
            ->first();

        $seasonId = $fixture->season_id;
        $leagueId = $fixture->league_id;

        $homeTeamFixtures = $this->getTeamFixtures($fixture->home_team_id, $seasonId, $leagueId);
        $awayTeamFixtures = $this->getTeamFixtures($fixture->away_team_id, $seasonId, $leagueId);

        return [
            $fixture->home_team_id => $this->parseTeamStats($homeTeamFixtures, $fixture, $fixture->home_team_id),
            $fixture->away_team_id => $this->parseTeamStats($awayTeamFixtures, $fixture, $fixture->away_team_id)
        ];
    }

    private function parseTeamStats($fixtures, $fixture, $teamId) {
        return [
            'goals' => [
                'total' => [
                    'scored' => $fixtures->filter(function($item) { return $item->playing_home; })->sum('ft_home_goals') + $fixtures->filter(function($item) { return !$item->playing_home; })->sum('ft_away_goals'),
                    'conceded' => $fixtures->filter(function($item) { return $item->playing_home; })->sum('ft_away_goals') + $fixtures->filter(function($item) { return !$item->playing_home; })->sum('ft_home_goals'),
                ],                    
                'home' => [
                    '1half' => [
                        'scored' => $fixtures->filter(function($item) { return $item->playing_home; })->sum('ht_home_goals'),
                        'conceded' => $fixtures->filter(function($item) { return $item->playing_home; })->sum('ht_away_goals')
                    ],
                    '2half' => [
                        'scored' => $fixtures->filter(function($item) { return $item->playing_home; })->sum('ft_home_goals') - $fixtures->filter(function($item) { return $item->playing_home; })->sum('ht_home_goals'),
                        'conceded' => $fixtures->filter(function($item) { return $item->playing_home; })->sum('ft_away_goals') - $fixtures->filter(function($item) { return $item->playing_home; })->sum('ht_away_goals')
                    ],
                ],
                'away' => [
                    '1half' => [
                        'scored' => $fixtures->filter(function($item) { return !$item->playing_home; })->sum('ht_away_goals'),
                        'conceded' => $fixtures->filter(function($item) { return !$item->playing_home; })->sum('ht_home_goals')
                    ],
                    '2half' => [
                        'scored' => $fixtures->filter(function($item) { return !$item->playing_home; })->sum('ft_away_goals') - $fixtures->filter(function($item) { return !$item->playing_home; })->sum('ht_away_goals'),
                        'conceded' => $fixtures->filter(function($item) { return !$item->playing_home; })->sum('ft_home_goals') - $fixtures->filter(function($item) { return !$item->playing_home; })->sum('ht_home_goals')
                    ],
                ],
                'minutes' => [
                    'scored' => $this->calculateGoalsMinutes($fixtures, $teamId, 'scored'),
                    'conceded' => $this->calculateGoalsMinutes($fixtures, $fixture->home_team_id == $teamId ? $fixture->away_team_id : $fixture->home_team_id, 'conceded')
                ],
                'best_scorer' => $this->getBestScorer($fixtures, $teamId)
            ],
            'btts' => [
                'total' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return $item->btts == 1; })->count() / $fixtures->count()) * 100, 1) : 0,
                'home' => [
                    'total' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return $item->playing_home && $item->btts == 1; })->count() / $fixtures->filter(function($item) { return $item->playing_home; })->count()) * 100, 1) : 0,
                    '1half' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return $item->playing_home && $item->btts_1half == 1; })->count() / $fixtures->filter(function($item) { return $item->playing_home; })->count()) * 100, 1) : 0,
                    '2half' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return $item->playing_home && $item->btts_2half == 1; })->count() / $fixtures->filter(function($item) { return $item->playing_home; })->count()) * 100, 1) : 0
                ],
                'away' => [
                    'total' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return !$item->playing_home && $item->btts == 1; })->count() / $fixtures->filter(function($item) { return !$item->playing_home; })->count()) * 100, 1) : 0,
                    '1half' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return !$item->playing_home && $item->btts_1half == 1; })->count() / $fixtures->filter(function($item) { return !$item->playing_home; })->count()) * 100, 1) : 0,
                    '2half' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return !$item->playing_home && $item->btts_2half == 1; })->count() / $fixtures->filter(function($item) { return !$item->playing_home; })->count()) * 100, 1) : 0
                ]
            ],
            'cards' => [
                'against' => [
                    'total' => $fixtures->count() > 0 ? round(($fixtures->sum('total_cards_against') / $fixtures->count()) * 100, 1) : 0,
                    'home' => [
                        'total' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return $item->playing_home; })->sum('total_cards_against') / $fixtures->filter(function($item) { return $item->playing_home; })->count()) * 100, 1) : 0,
                        '1half' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return $item->playing_home; })->sum('total_cards_against_1half') / $fixtures->filter(function($item) { return $item->playing_home; })->count()) * 100, 1) : 0,
                        '2half' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return $item->playing_home; })->sum('total_cards_against_2half') / $fixtures->filter(function($item) { return $item->playing_home; })->count()) * 100, 1) : 0
                    ],
                    'away' => [
                        'total' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return !$item->playing_home; })->sum('total_cards_against') / $fixtures->filter(function($item) { return !$item->playing_home; })->count()) * 100, 1) : 0,
                        '1half' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return !$item->playing_home; })->sum('total_cards_against_1half') / $fixtures->filter(function($item) { return !$item->playing_home; })->count()) * 100, 1) : 0,
                        '2half' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return !$item->playing_home; })->sum('total_cards_against_2half') / $fixtures->filter(function($item) { return !$item->playing_home; })->count()) * 100, 1) : 0
                    ]
                ],
                'infavor' => [
                    'total' => $fixtures->count() > 0 ? round(($fixtures->sum('total_cards_infavor') / $fixtures->count()) * 100, 1) : 0,
                    'home' => [
                        'total' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return $item->playing_home; })->sum('total_cards_infavor') / $fixtures->filter(function($item) { return $item->playing_home; })->count()) * 100, 1) : 0,
                        '1half' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return $item->playing_home; })->sum('total_cards_infavor_1half') / $fixtures->filter(function($item) { return $item->playing_home; })->count()) * 100, 1) : 0,
                        '2half' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return $item->playing_home; })->sum('total_cards_infavor_2half') / $fixtures->filter(function($item) { return $item->playing_home; })->count()) * 100, 1) : 0
                    ],
                    'away' => [
                        'total' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return !$item->playing_home; })->sum('total_cards_infavor') / $fixtures->filter(function($item) { return !$item->playing_home; })->count()) * 100, 1) : 0,
                        '1half' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return !$item->playing_home; })->sum('total_cards_infavor_1half') / $fixtures->filter(function($item) { return !$item->playing_home; })->count()) * 100, 1) : 0,
                        '2half' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return !$item->playing_home; })->sum('total_cards_infavor_2half') / $fixtures->filter(function($item) { return !$item->playing_home; })->count()) * 100, 1) : 0
                    ]
                ]
            ],
            'corners' => [
                'infavor' => [
                    'total' => $fixtures->count() > 0 ? round(($fixtures->sum('total_corners_infavor') / $fixtures->count()) * 100, 1) : 0,
                    'home' => [
                        'total' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return $item->playing_home; })->sum('total_corners_infavor') / $fixtures->filter(function($item) { return $item->playing_home; })->count()) * 100, 1) : 0,
                        '1half' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return $item->playing_home; })->sum('total_corners_infavor_1half') / $fixtures->filter(function($item) { return $item->playing_home; })->count()) * 100, 1) : 0,
                        '2half' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return $item->playing_home; })->sum('total_corners_infavor_2half') / $fixtures->filter(function($item) { return $item->playing_home; })->count()) * 100, 1) : 0
                    ],
                    'away' => [
                        'total' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return !$item->playing_home; })->sum('total_corners_infavor') / $fixtures->filter(function($item) { return !$item->playing_home; })->count()) * 100, 1) : 0,
                        '1half' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return !$item->playing_home; })->sum('total_corners_infavor_1half') / $fixtures->filter(function($item) { return !$item->playing_home; })->count()) * 100, 1) : 0,
                        '2half' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return !$item->playing_home; })->sum('total_corners_infavor_2half') / $fixtures->filter(function($item) { return !$item->playing_home; })->count()) * 100, 1) : 0
                    ]
                ],
                'against' => [
                    'total' => $fixtures->count() > 0 ? round(($fixtures->sum('total_corners_against') / $fixtures->count()) * 100, 1) : 0,
                    'home' => [
                        'total' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return $item->playing_home; })->sum('total_corners_against') / $fixtures->filter(function($item) { return $item->playing_home; })->count()) * 100, 1) : 0,
                        '1half' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return $item->playing_home; })->sum('total_corners_against_1half') / $fixtures->filter(function($item) { return $item->playing_home; })->count()) * 100, 1) : 0,
                        '2half' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return $item->playing_home; })->sum('total_corners_against_2half') / $fixtures->filter(function($item) { return $item->playing_home; })->count()) * 100, 1) : 0
                    ],
                    'away' => [
                        'total' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return !$item->playing_home; })->sum('total_corners_against') / $fixtures->filter(function($item) { return !$item->playing_home; })->count()) * 100, 1) : 0,
                        '1half' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return !$item->playing_home; })->sum('total_corners_against_1half') / $fixtures->filter(function($item) { return !$item->playing_home; })->count()) * 100, 1) : 0,
                        '2half' => $fixtures->count() > 0 ? round(($fixtures->filter(function($item) { return !$item->playing_home; })->sum('total_corners_against_2half') / $fixtures->filter(function($item) { return !$item->playing_home; })->count()) * 100, 1) : 0
                    ]
                ]
            ]
        ];
    }

    private function getBestScorer($fixtures, $teamId) {
        $players = [];

        foreach($fixtures as $fixture) {
            $playersScored = $fixture->players_scored ? explode(',', $fixture->players_scored) : [];
            foreach($playersScored as $playerId) {
                if (!isset($players[$playerId])) {
                    $players[$playerId] = 0;
                }
                $players[$playerId]++;
            }
        }

        arsort($players);

        if (sizeof($players) > 0) {
            $bestScorer = JFHPlayer::query()
                ->where('id', array_keys($players)[0])
                ->first();

            return [
                'player' => $bestScorer,
                'goals' => array_values($players)[0]
            ];
        }

        return null;
    }

    private function calculateGoalsMinutes($fixtures, $teamId, $type) {
        $minutesPercentageTable = [
            '15' => 0,
            '30' => 0,
            '45' => 0,
            '60' => 0,
            '75' => 0,
            '90' => 0
        ];

        foreach($fixtures as $fixture) {
            if ($type == 'scored') {
                $minutes = $fixture->elapsed_scored ? explode(',', $fixture->elapsed_scored) : [];
            } else {
                $minutes = $fixture->elapsed_conceded ? explode(',', $fixture->elapsed_conceded) : [];
            }
            
            foreach($minutes as $minute) {
                if (in_array($minute, range(0,15))) {
                    $minutesPercentageTable['15']++;
                }
                elseif (in_array($minute, range(16,30))) {
                    $minutesPercentageTable['30']++;
                }
                elseif (in_array($minute, range(31,45))) {
                    $minutesPercentageTable['45']++;
                }
                elseif (in_array($minute, range(46,60))) {
                    $minutesPercentageTable['60']++;
                }
                elseif (in_array($minute, range(61,75))) {
                    $minutesPercentageTable['75']++;
                }
                elseif (in_array($minute, range(76,90))) {
                    $minutesPercentageTable['90']++;
                }
            }
        }

        $n = array_sum(array_values($minutesPercentageTable));

        foreach($minutesPercentageTable as $key => $value) {
            if ($value > 0) {
                $minutesPercentageTable[$key] = round(($value / $n) * 100, 1);
            }
        }    

        return $minutesPercentageTable;
    }

    private function getTeamFixtures(int $teamId, int $seasonId, int $leagueId) 
    {
        return JFHFixture::query()
            ->leftJoin('jfh_fixtures_events', 'jfh_fixtures_events.fixture_id', '=', 'jfh_fixtures.id')
            ->leftJoin('jfh_players', 'jfh_players.id', '=', 'jfh_fixtures_events.player_id')
            ->where(function($q) use ($teamId) {
                $q->where('jfh_fixtures.home_team_id', $teamId)
                  ->orWhere('jfh_fixtures.away_team_id', $teamId);
            })
            ->where('jfh_fixtures.season_id', $seasonId)
            ->where('jfh_fixtures.league_id', $leagueId)
            ->whereIn('jfh_fixtures_events.type', ['Goal', 'Card', 'Corner'])
            ->where('jfh_fixtures_events.detail', '<>', 'Missed Penalty')
            ->whereIn('jfh_fixtures.status', ['FT', 'AET', 'PEN', 'PST', 'CANC', 'ABD', 'AWD', 'WO'])
            ->whereRaw('DATE(jfh_fixtures.starts_at) < DATE(NOW())')
            ->select([
                'jfh_fixtures.home_team_id',
                'jfh_fixtures.away_team_id',
                DB::raw('IF(jfh_fixtures.home_team_id = ' . $teamId . ', 1, 0) playing_home'),
                'jfh_fixtures.ft_home_goals',
                'jfh_fixtures.ft_away_goals',
                'jfh_fixtures.ht_home_goals',
                'jfh_fixtures.ht_away_goals',                
                DB::raw("SUM(IF(jfh_fixtures_events.type = 'Card' AND jfh_fixtures_events.team_id = " . $teamId . ",1,0)) total_cards_against"),
                DB::raw("SUM(IF(jfh_fixtures_events.type = 'Card' AND jfh_fixtures_events.team_id = " . $teamId . " AND jfh_fixtures_events.elapsed <= 45,1,0)) total_cards_against_1half"),
                DB::raw("SUM(IF(jfh_fixtures_events.type = 'Card' AND jfh_fixtures_events.team_id = " . $teamId . " AND jfh_fixtures_events.elapsed > 45,1,0)) total_cards_against_2half"),
                DB::raw("SUM(IF(jfh_fixtures_events.type = 'Card' AND jfh_fixtures_events.team_id <> " . $teamId . ",1,0)) total_cards_infavor"),
                DB::raw("SUM(IF(jfh_fixtures_events.type = 'Card' AND jfh_fixtures_events.team_id <> " . $teamId . " AND jfh_fixtures_events.elapsed <= 45,1,0)) total_cards_infavor_1half"),
                DB::raw("SUM(IF(jfh_fixtures_events.type = 'Card' AND jfh_fixtures_events.team_id <> " . $teamId . " AND jfh_fixtures_events.elapsed > 45,1,0)) total_cards_infavor_2half"),
                DB::raw("SUM(IF(jfh_fixtures_events.type = 'Corner' AND jfh_fixtures_events.team_id = " . $teamId . ",1,0)) total_corners_infavor"),
                DB::raw("SUM(IF(jfh_fixtures_events.type = 'Corner' AND jfh_fixtures_events.team_id = " . $teamId . " AND jfh_fixtures_events.elapsed <= 45,1,0)) total_corners_infavor_1half"),
                DB::raw("SUM(IF(jfh_fixtures_events.type = 'Corner' AND jfh_fixtures_events.team_id = " . $teamId . " AND jfh_fixtures_events.elapsed > 45,1,0)) total_corners_infavor_2half"),
                DB::raw("SUM(IF(jfh_fixtures_events.type = 'Corner' AND jfh_fixtures_events.team_id <> " . $teamId . ",1,0)) total_corners_against"),
                DB::raw("SUM(IF(jfh_fixtures_events.type = 'Corner' AND jfh_fixtures_events.team_id <> " . $teamId . " AND jfh_fixtures_events.elapsed <= 45,1,0)) total_corners_against_1half"),
                DB::raw("SUM(IF(jfh_fixtures_events.type = 'Corner' AND jfh_fixtures_events.team_id <> " . $teamId . " AND jfh_fixtures_events.elapsed > 45,1,0)) total_corners_against_2half"),
                DB::raw("IF(jfh_fixtures.ft_home_goals > 0 AND jfh_fixtures.ft_away_goals > 0,1,0) btts"),
                DB::raw("IF(jfh_fixtures.ht_home_goals > 0 AND jfh_fixtures.ht_away_goals > 0,1,0) btts_1half"),
                DB::raw("IF((jfh_fixtures.ft_home_goals - jfh_fixtures.ht_home_goals) > 0 AND (jfh_fixtures.ft_away_goals - jfh_fixtures.ht_away_goals) > 0,1,0) btts_2half"),
                DB::raw("GROUP_CONCAT(IF(jfh_fixtures_events.type = 'Goal' AND jfh_fixtures_events.detail != 'Missed Penalty' AND jfh_fixtures_events.team_id = " . $teamId . ", jfh_fixtures_events.elapsed, NULL)) elapsed_scored"),
                DB::raw("GROUP_CONCAT(IF(jfh_fixtures_events.type = 'Goal' AND jfh_fixtures_events.detail != 'Missed Penalty' AND jfh_fixtures_events.team_id <> " . $teamId . ", jfh_fixtures_events.elapsed, NULL)) elapsed_conceded"),
                DB::raw("GROUP_CONCAT(IF(jfh_fixtures_events.type = 'Goal' AND jfh_fixtures_events.detail != 'Missed Penalty' AND jfh_fixtures_events.team_id = " . $teamId . ", jfh_players.id,NULL)) players_scored"),
            ])
            ->groupBy('jfh_fixtures.id')
            ->get();
    }

    public function getStandingsForFixture($fixture) {
        return JFHStanding::query()
            ->where('season_id', $fixture->season_id)
            ->where('league_id', $fixture->league_id)
            ->orderBy('rank')
            ->get();
    }
}