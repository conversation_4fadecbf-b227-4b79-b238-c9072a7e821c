<?php

namespace App\Http\Controllers\ApostasDePescada\PagesHandler\PagesListeners;

use App\Helpers\Utils;
use App\Libs\Filters\ExactFilter;
use Illuminate\Http\Request;
use App\Libs\HandleCache\HandleCache;
use App\Models\APOrder;
use App\Models\JFHAiMessage;
use App\Models\JFHLiveOdd;
use App\Models\Site;
use App\Models\SiteUser;
use App\Models\User;

class PagesListener extends Listener
{
    private $param1;
    private $param2;
    private $site;

    public function __construct($param1, $param2)
    {
        $this->param1 = $param1;
        $this->param2 = $param2;

        $this->site = HandleCache::setOrGetStaticQuery(
            Site::query()->where('url', env('APP_ROLE')),
            true
        );
    }

    public function apply(Request $request, $repos)
    {
        if (!$this->getSelected()) {
            $map = [
                'politica-de-cookies' => [$this, 'cookies'],
                'politica-de-privacidade' => [$this, 'privacy'],
                'termos-e-condicoes' => [$this, 'terms'],
                'settings' => [$this, 'settings'],
                'sitemap' => [$this, 'sitemap'],
                'messages' => [$this, 'messages'],
                'pro' => [$this, 'pro'],
                'callbacks' => [$this, 'callbacks'],
                'live' => [$this, 'live']
            ];

            if (($this->param1) && (in_array($this->param1, array_keys($map)))) {
                return $map[$this->param1]($request, $repos);
            }

            return -1;
        }
    }

    public function terms(Request $request, $repos) { 
        $this->select([
            'html' => \App\Helpers\Terms::getTermsBlog(\App\Models\Site::query()->where(['url' => $this->site->url])->first()),
            'title' => 'Termos e Condições | Apostas de Pescada',
            'description' => 'Conheça os Termos e Condições que regem o uso do nosso site. Transparência e clareza para garantir uma experiência segura e confiável.'
        ]);

        return;
    }

    public function cookies(Request $request, $repos) {
        $this->select([
            'html' => \App\Helpers\Terms::getCookiesBlog(\App\Models\Site::query()->where(['url' => $this->site->url])->first()),
            'title' => 'Política de Cookies | Apostas de Pescada',
            'description' => 'Saiba como utilizamos cookies para melhorar a sua experiência de navegação. Veja quais dados são coletados e como pode gerir as suas preferências.'
        ]);

        return;
    }

    public function privacy(Request $request, $repos) { 
        $this->select([
            'html' => \App\Helpers\Terms::getPrivacyBlog(\App\Models\Site::query()->where(['url' => $this->site->url])->first()),
            'title' => 'Política de Privacidade | Apostas de Pescada',
            'description' => 'Entenda como protegemos os seus dados pessoais. Confira a nossa Política de Privacidade e saiba como garantimos segurança e transparência.'
        ]);

        return;
    }
    
    public function settings(Request $request, $repos) {
        $siteUser = SiteUser::query()
            ->where('token', Utils::crypt($request->get('hash'), 'AP', 'AP', 'd'))
            ->first();

        $user = User::find($siteUser->user_id)->toArray();
        $user['newsletter'] = (bool) $siteUser->newsletter;
        $user['pro_plan'] = $user['pro_plan'];
        $user['has_trial_pro_plan'] = $user['has_trial_pro_plan'];

        $this->select([
            'user' => $user,
            'betting_sites' => $repos['bettingHousesRepo']->getAll([
                new ExactFilter('include_at_pro', 1)
            ])->getResult(),
        ]);
    }

    public function messages(Request $request, $repos) {
        $siteUser = SiteUser::query()
            ->where('token', Utils::crypt($request->get('hash'), 'AP', 'AP', 'd'))
            ->first();

        $messages = JFHAiMessage::query()
            ->where('user_id', $siteUser->user_id)
            ->orderBy('sent_at', 'desc')
            ->limit(10)
            ->offset($request->get('offset', 0))
            ->get();

        $count = JFHAiMessage::query()
            ->where('user_id', $siteUser->user_id)
            ->whereRaw('DATE(sent_at) = DATE(NOW())')
            ->count();

        $this->select([
            'messages' => $messages->sortBy('sent_at')->values()->all(),
            'count' => $count,
            'limit' => 10,
        ]);
    }

    public function pro(Request $request, $repos) {        
        $this->select([
            'betting_sites' => $repos['bettingHousesRepo']->getAll([
                new ExactFilter('include_at_pro', 1)
            ])->getResult(),
        ]);
    }

    public function callbacks(Request $request, $repos) {
        $status = 'NOK';

        if (($request->get('key') == 'Apostas#de$pescada2025') && (in_array($this->param2, ['mb', 'mbway']))) {
            $order = APOrder::find($request->get('orderId'));
            $order->status = 'PAID';
            $order->save();

            $status = 'OK';

            $user = User::find($order->user_id);
            $user->pro_plan = '2030-12-31';
            $user->save();
        }

        $this->select([
            'status' => $status
        ]);
    }

    public function live(Request $request, $repos) {
        $fixture = $repos['jogosFutebolHoje\JFHFixturesRepo']->getById($this->param2);

        $fixtureLiveOdds = JFHLiveOdd::query()
            ->with(['Odds', 'Odds.Values'])
            ->where('fixture_id', 37227/*$this->param2*/)
            ->first();

        $this->select([
            'facts' => [],
            'stats' => $repos['jogosFutebolHoje\JFHStatsRepo']->getProLive(23666),
            'odds' => $fixtureLiveOdds,
            'standings' => $fixture->standings
        ]);
    }
}
