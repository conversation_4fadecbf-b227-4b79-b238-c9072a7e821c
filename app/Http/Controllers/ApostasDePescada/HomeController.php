<?php

namespace App\Http\Controllers\ApostasDePescada;

use App\Http\Controllers\ApostasDePescada\PagesHandler\PageHandler;
use App\Http\Controllers\ApostasDePescada\PagesHandler\PagesListeners\AjaxListener;
use App\Http\Controllers\ApostasDePescada\PagesHandler\PagesListeners\BettingSitesListener;
use App\Http\Controllers\ApostasDePescada\PagesHandler\PagesListeners\BlogListener;
use App\Http\Controllers\ApostasDePescada\PagesHandler\PagesListeners\CalendarListener;
use App\Http\Controllers\ApostasDePescada\PagesHandler\PagesListeners\DefaultListener;
use App\Http\Controllers\ApostasDePescada\PagesHandler\PagesListeners\FeaturedFixturesListener;
use App\Http\Controllers\ApostasDePescada\PagesHandler\PagesListeners\FixturesListener;
use App\Http\Controllers\ApostasDePescada\PagesHandler\PagesListeners\LeaguesListener;
use App\Http\Controllers\ApostasDePescada\PagesHandler\PagesListeners\PagesListener;
use App\Http\Controllers\ApostasDePescada\PagesHandler\PagesListeners\SearchListener;
use App\Http\Controllers\ApostasDePescada\PagesHandler\PagesListeners\StatsListener;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Repos\ArticlesRepo;
use App\Repos\BettingHousesRepo;
use App\Repos\CategoriesRepo;
use App\Repos\JogosFutebolHoje\JFHFactsRepo;
use App\Repos\JogosFutebolHoje\JFHFixturesRepo;
use App\Repos\JogosFutebolHoje\JFHFixturesStatsRepo;
use App\Repos\JogosFutebolHoje\JFHLeaguesRepo;
use App\Repos\JogosFutebolHoje\JFHSearchRepo;
use App\Repos\JogosFutebolHoje\JFHSeasonsRepo;
use App\Repos\JogosFutebolHoje\JFHStadingsRepo;
use App\Repos\JogosFutebolHoje\JFHStatsRepo;
use App\Repos\JogosFutebolHoje\JFHTeamsRepo;
use App\Repos\JogosFutebolHoje\JFHTeamsStatsRepo;
use App\Repos\JogosFutebolHoje\JFHEventsRepo;

class HomeController extends Controller
{
    public function index(
        Request $request,
        JFHFixturesRepo $jFHFixturesRepo,
        BettingHousesRepo $bettingHousesRepo,
        CategoriesRepo $categoriesRepo,
        ArticlesRepo $articlesRepo,
        JFHTeamsStatsRepo $jFHTeamsStatsRepo,
        JFHStadingsRepo $jFHStadingsRepo,
        JFHFactsRepo $jFHFactsRepo,
        JFHFixturesStatsRepo $jFHFixturesStatsRepo,
        JFHStatsRepo $jFHStatsRepo,
        JFHLeaguesRepo $jFHLeaguesRepo,
        JFHTeamsRepo $jFHTeamsRepo,
        JFHSeasonsRepo $jFHSeasonsRepo,
        JFHSearchRepo $jFHSearchRepo,
        JFHEventsRepo $jFHEventsRepo,
        $param1 = null,
        $param2 = null,
        $param3 = null
    ) {
        $pageHandle = new PageHandler(
            $jFHFixturesRepo,
            $bettingHousesRepo,
            $categoriesRepo,
            $articlesRepo,
            $jFHTeamsStatsRepo,
            $jFHStadingsRepo,
            $jFHFactsRepo,
            $jFHFixturesStatsRepo,
            $jFHStatsRepo,
            $jFHLeaguesRepo,
            $jFHTeamsRepo,
            $jFHSeasonsRepo,
            $jFHSearchRepo,
            $jFHEventsRepo
        );
        $pageHandle->addListener(new DefaultListener($param1));
        $pageHandle->addListener(new AjaxListener($param1));
        $pageHandle->addListener(new LeaguesListener($param1));
        $pageHandle->addListener(new SearchListener($param1));
        $pageHandle->addListener(new PagesListener($param1, $param2));
        $pageHandle->addListener(new FeaturedFixturesListener($param1));
        $pageHandle->addListener(new StatsListener($param1, $param2));
        $pageHandle->addListener(new CalendarListener($param1, $param2));
        $pageHandle->addListener(new FixturesListener($param1, $param2, $param3));
        $pageHandle->addListener(new BettingSitesListener($param1, $param2));
        $pageHandle->addListener(new BlogListener($param1, $param2, $param3));

        $result = $pageHandle->handle($request);

        if ($result) {
            $this->findInvalidUtf8($result);
            dd('-');
            return response()
                ->json($result);
        }

        return response()
            ->json([], 404);
    }

    private function findInvalidUtf8($data, $path = '')
    {
        if (is_array($data) || is_object($data)) {
            foreach ($data as $key => $value) {
                $newPath = $path === '' ? $key : "$path.$key";
                $this->findInvalidUtf8($value, $newPath);
            }
        } else {
            if (is_string($data) && !mb_check_encoding($data, 'UTF-8')) {
                dump("⚠️ UTF-8 inválido em: $path", $data);
            }
        }
    }
}
