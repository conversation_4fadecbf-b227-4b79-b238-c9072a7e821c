<?php

namespace App\Http\Controllers\ApostasDePescada;

use App\Helpers\Utils;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Jobs\ApostasDePescada\DispatchContactEmail;
use App\Libs\Payments\IfThenElseGateway;
use App\Models\JFHAiMessage;
use App\Models\JFHFixture;
use App\Models\JFHGuess;
use App\Models\Site;
use App\Models\SiteNewsletter;
use App\Models\SiteUser;
use App\Models\User;
use Illuminate\Support\Facades\Validator;

class PostController extends Controller
{
    use DispatchesJobs;

    public function guesses(Request $request) {        
        $payload = json_decode(file_get_contents('php://input'), true);
        if (!empty($request->all())) {
            $payload = $request->all();
        }

        $user = SiteUser::query()
            ->with(['User'])
            ->where('token', $payload['token'])
            ->first();

        $fixture = JFHFixture::find($payload['fixture_id']);

        if ($user && $fixture) {
            $userGuess = JFHGuess::firstOrNew(['user_id' => $user->User->id, 'fixture_id' => $fixture->id]);
            $userGuess->winner_id = $payload['winner'] == '1' ? $fixture->home_team_id : ($payload['winner'] == '2' ? $fixture->away_team_id : null);
            $userGuess->save();

            return response()->json(['status' => 'OK']);
        }

        return response()->json(['status' => 'NOK']);
    }
    
    public function contacts(Request $request) {
        $payload = json_decode(file_get_contents('php://input'), true);

        $rules = [
            //'captcha' => 'required|captcha',
            'name' => 'required',
            'email' => 'required',
            'message' => 'required'
        ];
        $validator = Validator::make($payload, $rules);

        if ($validator->fails()) {
            return response()->json(['status' => 'NOK', 'errors' => $validator->errors()]);
        }

        $this->dispatch(new DispatchContactEmail($payload));

        return response()->json(['status' => 'OK']);
    }    

    public function newsletter(Request $request) {
        $site = Site::query()
            ->where('url', env('APP_ROLE'))
            ->first();

        $payload = json_decode(file_get_contents('php://input'), true);

        if (!empty($payload['email'])) {
            SiteNewsletter::firstOrCreate(['site_id' => $site->id, 'email' => trim(strtolower($payload['email']))]);
            return response()->json(['status' => 'OK']);
        }

        return response()->json(['status' => 'NOK']);
    }

    public function settings(Request $request) {
        $site = \App\Models\Site::query()->where(['url' => env('APP_ROLE')])->first();

        $payload = json_decode(file_get_contents('php://input'), true);
        if (!empty($request->all())) {
            $payload = $request->all();
        }

        $siteUser = SiteUser::query()
            ->where('token', Utils::crypt($payload['token'], 'AP', 'AP', 'd'))
            ->where('site_id', $site->id)
            ->first();

        $user = User::query()
            ->where('id', $siteUser->user_id)
            ->first();

        if (!$siteUser || !$user) {
            return response()->json(['status' => 'NOK']);
        }

        $user->fill([
            'gender' => $payload['gender'],
            'birthdate' => $payload['birthdate'],
            'telephone' => $payload['telephone'],
            'name' => $payload['name'],
        ]);
        $user->save();

        SiteUser::query()
            ->where('id', $siteUser->id)
            ->update(['newsletter' => (int) $payload['newsletter']]);

        return response()->json(['status' => 'OK']);
    }

    public function proTrial(Request $request) {
        $site = \App\Models\Site::query()->where(['url' => env('APP_ROLE')])->first();

        $payload = json_decode(file_get_contents('php://input'), true);
        if (!empty($request->all())) {
            $payload = $request->all();
        }

        $siteUser = SiteUser::query()
            ->where('token', Utils::crypt($payload['token'], 'AP', 'AP', 'd'))
            ->where('site_id', $site->id)
            ->first();

        $user = User::query()
            ->where('id', $siteUser->user_id)
            ->first();

        if (!$siteUser || !$user) {
            return response()->json(['status' => 'NOK']);
        }

        $user->has_trial_pro_plan = 1;
        $user->pro_plan = date('Y-m-d', strtotime('+7 days'));
        $user->save();

        return response()->json(['status' => 'OK']);
    }

    public function proPlan(Request $request) {
        $site = \App\Models\Site::query()->where(['url' => env('APP_ROLE')])->first();

        $payload = json_decode(file_get_contents('php://input'), true);
        if (!empty($request->all())) {
            $payload = $request->all();
        }

        $siteUser = SiteUser::query()
            ->where('token', Utils::crypt($payload['token'], 'AP', 'AP', 'd'))
            ->where('site_id', $site->id)
            ->first();

        $user = User::query()
            ->where('id', $siteUser->user_id)
            ->first();

        if (!$siteUser || !$user) {
            return response()->json(['status' => 'NOK']);
        }

        $user->pro_plan = '2030-12-31';
        $user->save();

        return response()->json(['status' => 'OK']);
    }

    public function ai(Request $request) {
        $site = \App\Models\Site::query()->where(['url' => env('APP_ROLE')])->first();
        
        $payload = json_decode(file_get_contents('php://input'), true);
        if (!empty($request->all())) {
            $payload = $request->all();
        }

        if (empty($payload['message'])) {
            return response()->json(['status' => 'NOK']);
        }

        $siteUser = SiteUser::query()
            ->where('token', Utils::crypt($payload['token'], 'AP', 'AP', 'd'))
            ->where('site_id', $site->id)
            ->first();

        JFHAiMessage::create([
            'message' => ['message' => $payload['message']],
            'user_id' => $siteUser->user_id,
            'is_prompt' => true,
            'sent_at' => date('Y-m-d H:i:s'),
            '_id' => $payload['_id']
        ]);

        $messages = [
            [
                'role' => 'system',
                'content' => 'Seja preciso e conciso nas respostas. Utilize o conteúdo fornecido na próxima mensagem do utilizador como contexto principal para responder às perguntas. Não considere informações externas além deste texto. Se não souber, diga que não sabe.'
            ],
            [
                "role" => "user",
                "content" => "Lista de próximos jogos: \n Benfica - Sporting: em 2025-10-01 20:00:00. A odd para vitória do Benfica é de 1.5. \n\n" . $payload['message']
            ]
        ];

        $prompt = [
            'model' => 'sonar',
            'messages' => $messages,
            'response_format' => [
                'type' => 'json_schema',
                'json_schema' => [
                    'schema' => [
                        'type' => 'object',
                        'properties' => [
                            'message' => [
                                'type' => 'string'
                            ],                            
                        ],
                        'required' => ['message']
                    ]
                ]
            ],
            'disable_search' => true,
        ];

        $response = Utils::PerplexityAPI($prompt);

        if ($response['code'] == 200) {
            $message = JFHAiMessage::create([
                'message' => json_decode($response['response']->choices[0]->message->content),
                'user_id' => $siteUser->user_id,
                'is_prompt' => false,
                'full_message' => $response['response'],
                'sent_at' => date('Y-m-d H:i:s'),
                'price' => $response['response']->usage->cost->total_cost,
                '_id' => md5(uniqid())
            ]);
        }

        $count = JFHAiMessage::query()
            ->where('user_id', $siteUser->user_id)
            ->whereRaw('DATE(sent_at) = DATE(NOW())')
            ->count();

        return response()->json([
            'status' => $response['code'] == 200 ? 'OK' : 'NOK', 
            'response' => $response['response'], 
            'message' => $message,
            'count' => $count,
            'limit' => 10,
        ]);
    }

    public function payment(Request $request) {
        $site = \App\Models\Site::query()->where(['url' => env('APP_ROLE')])->first();

        $payload = json_decode(file_get_contents('php://input'), true);
        if (!empty($request->all())) {
            $payload = $request->all();
        }

        $siteUser = SiteUser::query()
            ->where('token', Utils::crypt($payload['token'], 'AP', 'AP', 'd'))
            ->where('site_id', $site->id)
            ->first();

        $user = User::query()
            ->where('id', $siteUser->user_id)
            ->first();

        $ifThenElseGateway = new IfThenElseGateway();

        if ($payload['payment_method'] == 'mb') {
            $result = $ifThenElseGateway->generateMb($user);
        } else {
            $result = $ifThenElseGateway->generateMbway($user, $payload['telephone']);
        }

        return response()->json(['status' => 'OK', 'result' => $result]);
    }
}
