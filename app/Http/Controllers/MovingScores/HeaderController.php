<?php

namespace App\Http\Controllers\MovingScores;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Repos\CategoriesRepo;
use App\Libs\Filters\ExactFilter;
use App\Libs\Filters\InFilter;
use App\Models\SiteCategory;
use App\Models\Site;

class HeaderController extends Controller
{
    private $siteUrl = 'api.movingscores.com';

    private $categoriesRepo;
    private $siteCategory;
    private $site;
    private $categoriesIds;

    public function __construct(
        CategoriesRepo $categoriesRepo,
        SiteCategory $siteCategory,
        Site $site
    ) {
        $this->categoriesRepo = $categoriesRepo;
        $this->siteCategory = $siteCategory;

        $this->site = $site::query()
            ->where('url', $this->siteUrl)
            ->first();

        $this->categoriesIds = $this->siteCategory::query()
            ->addFilters([
                new ExactFilter('site_id', $this->site->id)
            ])
            ->get()
            ->map(function ($item) {
                return $item->category_id;
            })
            ->toArray();
    }

    public function index(Request $request)
    {
        return response()->json([
            'categories' => $this->categoriesRepo->all([
                new ExactFilter('type', 'BLOG_ARTICLE'),
                new InFilter('id', $this->categoriesIds)
            ])
        ]);
    }
}
