<?php

namespace App\Http\Controllers\MovingScores;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Jobs\MovingScores\DispatchRecoverPassword;
use App\Models\Site;
use App\Models\SiteUser;
use App\Models\User;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class AuthController extends Controller
{
    use DispatchesJobs;

    private $siteUrl = 'api.movingscores.com';
    private $site;
    private $siteUser;
    private $user;

    public function __construct(
        Site $site,
        SiteUser $siteUser,
        User $user
    ) {
        $this->site = $site::query()
            ->where('url', $this->siteUrl)
            ->first();

        $this->siteUser = $siteUser;
        $this->user = $user;
    }

    public function login(Request $request)
    {
        $credentials = $request->only('email', 'password');
        $credentials['site_id'] = $this->site->id;

        if (Auth::attempt($credentials)) {
            return response()->json(Auth::user());
        }

        return response()->json(null, 401);
    }

    public function register(Request $request)
    {
        $name = ucwords(trim($request->get('name')));
        $email = trim(strtolower($request->get('email')));

        $siteUser = $this->siteUser::query()
            ->where('site_id', $this->site->id)
            ->where('email', $email)
            ->first();

        if ($siteUser) {
            return response()->json([
                'error' => 'User already exists',
                'message' => 'Já existe um utilizador com o e-mail indicado. Faça login.'
            ], 200); // already exists
        }

        $user = $this->user::query()
            ->where('email', $email)
            ->first();

        if (!$user) {
            $user = new $this->user();
            $user->name = $name;
            $user->email = $email;
            $user->setPassword($request->get('password'));
            $user->ip = $request->ip();
            $user->slug = str_slug($user->name);
            $user->save();
        }

        $siteUser = $this->siteUser::firstOrNew(['site_id' => $this->site->id, 'user_id' => $user->id]);
        $siteUser->removeFieldsFromRules(['captcha', 'password']);
        $siteUser->email = $user->email;
        $siteUser->setPassword($request->get('password'));
        $siteUser->name = $user->name;
        $siteUser->save();

        Auth::login($siteUser, true);

        return response()->json(Auth::user());
    }

    public function check(Request $request)
    {
        $name = ucwords(trim($request->get('name')));
        $email = trim(strtolower($request->get('email')));
        $googleId = $request->get('sub');

        $site = $this->site::query()
            ->where('url', $this->siteUrl)
            ->first();

        $siteUser = $this->siteUser::query()
            ->where('site_id', $this->site->id)
            ->where('email', $email)
            ->first();

        $user = $this->user::query()
            ->where('id', $siteUser->user_id)
            ->first();

        if ($siteUser && $user) {
            return response()->json($siteUser->toArray());
        }

        $hasAvatar = false;
        try {
            file_put_contents(env('APP_IMAGESPATH', storage_path('app/public/')) . $googleId . '.jpg', file_get_contents($request->get('picture')));
            $hasAvatar = true;
        } catch (\Exception $e) {
        }

        $password = md5($email);

        $user = $this->user::query()
            ->where('email', $email)
            ->first();

        if (!$user) {
            $user = new $this->user();
            $user->name = $name;
            $user->email = $email;
            $user->setPassword($password);
            $user->ip = $request->ip();
            $user->slug = str_slug($user->name);
        }

        $user->googleId = $googleId;

        if ($hasAvatar) {
            $user->avatar = $googleId . '.jpg';
        }

        $user->save();

        $siteUser = $this->siteUser::firstOrNew(['site_id' => $site->id, 'user_id' => $user->id]);
        $siteUser->removeFieldsFromRules(['captcha', 'password']);
        $siteUser->email = $user->email;
        $siteUser->setPassword($password);
        $siteUser->name = $user->name;
        $siteUser->save();

        $output = array_merge([], $siteUser->toArray(), ['pro_plan' => $user->pro_plan, 'has_trial_pro_plan' => $user->has_trial_pro_plan]);

        return response()->json($output);
    }

    public function recoverPassword(Request $request)
    {
        $email = trim(strtolower($request->get('email')));

        $siteUser = $this->siteUser::query()
            ->where('site_id', $this->site->id)
            ->where('email', $email)
            ->first();

        if ($siteUser) {
            $request->validate([
                'email' => 'required|email|exists:users',
            ]);

            $token = Str::random(64);

            DB::table('password_resets')->insert([
                'email' => $request->email,
                'token' => $token,
                'created_at' => Carbon::now()
            ]);

            $this->dispatch(new DispatchRecoverPassword([
                'email' => $request->email,
                'token' => $token
            ]));

            return response()->json([
                'status' => 'OK'
            ]);
        }

        return response()->json([
            'status' => 'NOK'
        ]);
    }

    public function resetPassword(Request $request)
    {
        $token = trim(strtolower($request->get('token')));

        $validator = Validator::make($request->all(), [
            'password' => 'required|confirmed',
            'token' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'NOK'
            ]);
        }

        $password = $request->password;

        $tokenData = DB::table('password_resets')
            ->where('token', $token)
            ->whereRaw('NOW() BETWEEN created_at AND DATE_ADD(created_at, INTERVAL 1 HOUR)')
            ->first();

        if (!$tokenData) {
            return response()->json([
                'status' => 'NOK'
            ]);
        }

        $siteUser = $this->siteUser::query()
            ->where('site_id', $this->site->id)
            ->where('email', $tokenData->email)
            ->first();

        if (!$siteUser) {
            return response()->json([
                'status' => 'NOK'
            ]);
        }

        $siteUser->removeFieldsFromRules(['captcha', 'password']);
        $siteUser->setPassword($password);
        $siteUser->save();

        DB::table('password_resets')
            ->where('email', $tokenData->email)
            ->delete();

        return response()->json([
            'status' => 'OK'
        ]);
    }
}
