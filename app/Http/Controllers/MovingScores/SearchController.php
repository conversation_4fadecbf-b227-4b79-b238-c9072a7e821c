<?php

namespace App\Http\Controllers\MovingScores;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Site;
use App\Repos\JogosFutebolHoje\JFHLeaguesRepo;
use App\Repos\JogosFutebolHoje\JFHSearchRepo;
use App\Libs\Filters\ExactFilter;

class SearchController extends Controller
{
    private $siteUrl = 'api.movingscores.com';
    private $site;
    private $jFHSearchRepo;
    private $jFHLeaguesRepo;

    public function __construct(
        JFHSearchRepo $jFHSearchRepo,
        JFHLeaguesRepo $jFHLeaguesRepo,
        Site $site
    ) {
        $this->site = $site::query()
            ->where('url', $this->siteUrl)
            ->first();
        $this->jFHSearchRepo = $jFHSearchRepo;
        $this->jFHLeaguesRepo = $jFHLeaguesRepo;
    }

    public function index(Request $request)
    {
        return response()->json([
            'results' => collect($this->jFHSearchRepo->search($request->get('q')))->groupBy('type'),
            'suggestions' => [
                //'fixtures' => $repos['jogosFutebolHoje\JFHFixturesRepo']->getNext(null, null, true),
                'leagues' => $this->jFHLeaguesRepo->getAll([
                    new ExactFilter('in_use', 1)
                ])->getResult()->map(function ($item) {
                    return [
                        'country_name' => $item->country->name,
                        'extra' => $item->country->name,
                        'filename' => $item->filename,
                        'id' => $item->id,
                        'link' => '/futebol',
                        'name' => $item->name,
                        'slug' => $item->slug,
                        'type' => 'leagues'
                    ];
                })
            ],
        ]);
    }
}
