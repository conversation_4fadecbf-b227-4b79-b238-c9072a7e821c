<?php

namespace App\Http\Controllers\MovingScores\Football;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Libs\Filters\ExactFilter;
use App\Repos\JogosFutebolHoje\JFHLeaguesRepo;
use App\Repos\JogosFutebolHoje\JFHLeaguesSeasonsRepo;

class Leagues extends Controller
{
    private $jFHLeaguesRepo;
    private $jFHLeaguesSeasonsRepo;

    public function __construct(
        JFHLeaguesRepo $jFHLeaguesRepo,
        JFHLeaguesSeasonsRepo $jFHLeaguesSeasonsRepo
    ) {
        $this->jFHLeaguesRepo = $jFHLeaguesRepo;
        $this->jFHLeaguesSeasonsRepo = $jFHLeaguesSeasonsRepo;
    }

    public function show(Request $request, $id)
    {
        $id = intval($id);

        $league = $this->jFHLeaguesRepo->get($id)->getResult();

        return response()->json([
            'league' => $league,
            'seasons' => $this->jFHLeaguesSeasonsRepo->getAll([
                new ExactFilter('league_id', $league->id)
            ])->getResult()
        ]);
    }
}
