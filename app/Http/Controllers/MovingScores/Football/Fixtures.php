<?php

namespace App\Http\Controllers\MovingScores\Football;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Repos\JogosFutebolHoje\JFHFixturesRepo;
use App\Repos\JogosFutebolHoje\JFHEventsRepo;
use App\Repos\JogosFutebolHoje\JFHStatsRepo;

class Fixtures extends Controller
{
    private $jFHFixturesRepo;
    private $jFHEventsRepo;
    private $jFHStatsRepo;

    public function __construct(
        JFHFixturesRepo $jFHFixturesRepo,
        JFHEventsRepo $jFHEventsRepo,
        JFHStatsRepo $jFHStatsRepo
    ) {
        $this->jFHFixturesRepo = $jFHFixturesRepo;
        $this->jFHEventsRepo = $jFHEventsRepo;
        $this->jFHStatsRepo = $jFHStatsRepo;
    }

    public function index(Request $request)
    {
        return response()->json(
            $this->jFHFixturesRepo->getNext(null, null, true)
        );
    }

    public function show(Request $request, $id)
    {
        $id = intval($id);

        return response()->json(
            $this->jFHFixturesRepo->getById($id)
        );
    }

    public function events(Request $request, $id)
    {
        return response()->json(
            $this->jFHEventsRepo->getAll($id)
        );
    }

    public function stats(Request $request, $id)
    {
        return response()->json(
            $this->jFHStatsRepo->getAll($id)
        );
    }
}
