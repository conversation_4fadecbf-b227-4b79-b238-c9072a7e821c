<?php

namespace App\Http\Controllers\MovingScores\Football;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Repos\GoalsRepo;

class Highlights extends Controller
{
    private $goalsRepo;

    public function __construct(GoalsRepo $goalsRepo)
    {
        $this->goalsRepo = $goalsRepo;
    }

    public function index(Request $request)
    {
        return response()->json(
            $this->goalsRepo->getAll()->getResult()
        );
    }
}
