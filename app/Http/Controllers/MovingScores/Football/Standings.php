<?php

namespace App\Http\Controllers\MovingScores\Football;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Repos\JogosFutebolHoje\JFHLeaguesRepo;
use App\Repos\JogosFutebolHoje\JFHStadingsRepo;
use App\Libs\Filters\ExactFilter;
use App\Libs\Filters\InFilter;
use App\Models\JFHSeason;

class Standings extends Controller
{
    private $jFHLeaguesRepo;
    private $jFHStadingsRepo;

    public function __construct(
        JFHLeaguesRepo $jFHLeaguesRepo,
        JFHStadingsRepo $jFHStadingsRepo
    ) {
        $this->jFHLeaguesRepo = $jFHLeaguesRepo;
        $this->jFHStadingsRepo = $jFHStadingsRepo;
    }

    public function index(Request $request)
    {
        $standings = $this->jFHLeaguesRepo->getAll([
            new ExactFilter('display_standings', 1)
        ])->getResult();

        $season = JFHSeason::query()
            ->where('is_current', 1)
            ->first();

        return response()->json(
            $this->jFHStadingsRepo->getAll([
                new InFilter('league_id', $standings->map(function ($league) {
                    return $league->id;
                })->toArray()),
                new ExactFilter('season_id', $season->id)
            ])->getResult()->groupBy('league_id')
        );
    }
}
