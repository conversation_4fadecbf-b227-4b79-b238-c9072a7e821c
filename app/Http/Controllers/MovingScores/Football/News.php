<?php

namespace App\Http\Controllers\MovingScores\Football;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Libs\Filters\ExactFilter;
use App\Models\Site;

class News extends Controller
{
    private $siteUrl = 'api.movingscores.com';

    private $articlesRepo;
    private $site;

    public function __construct(
        Site $site
    ) {
        $this->site = $site::query()
            ->where('url', $this->siteUrl)
            ->first();
    }

    public function latest(Request $request)
    {
        return response()->json($this->articlesRepo->getPaginated([
            new ExactFilter('is_carousel', 1),
            new ExactFilter('is_hero', 0),
            // new ExactFilter('site_id', $this->site->id) @TODO: Delete later
        ]));
    }
}
