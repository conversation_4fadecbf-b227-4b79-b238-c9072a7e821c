<?php
/**
 * Created by PhpStorm.
 * User: pedrodavide
 * Date: 28/08/19
 * Time: 19:34
 */

namespace App\Helpers;

use App\Models\Account;
use App\Models\Alert;
use App\Models\Banner;
use App\Models\Connection;
use App\Models\Country;
use App\Models\Event;
use App\Models\IataCode;
use App\Models\JFHApi;
use App\Models\KInfluencer;
use App\Models\KInfluencerStory;
use App\Models\Market;
use App\Models\MarketValue;
use App\Models\Payment;
use App\Models\Season;
use App\Models\Site;
use App\Models\Team;
use Coduo\PHPHumanizer\NumberHumanizer;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;
use WebPConvert\WebPConvert;

class Utils
{
    private static $medias_types = [
        1 => 'IMAGE',
        2 => 'VIDEO'
    ];

    public static function isFollowing($id) {
        if (!Auth::check()) {
            return false;
        }

        return Connection::query()
            ->where('from_user_id', Auth::user()->id)
            ->where('to_user_id', $id)
            ->first();
    }

    public static function hasAlert($from, $to) {
        if (!Auth::check()) {
            return false;
        }

        $from = IataCode::query()->where('code', $from)->first()->id;
        $to = IataCode::query()->where('code', $to)->first()->id;

        return Alert::query()
            ->where('from_iata_code_id', $from)
            ->where('to_iata_code_id', $to)
            ->where('site_user_id', Auth::user()->id)
            ->first();
    }

    public static function getBanner($id) {
        $site = Site::query()
            ->where('url', env('APP_ROLE'))
            ->first();

        $banner = Banner::query()
            ->where('id', $id)
            ->where('site_id', $site->id)
            ->where('active', 1)
            ->first();

        if (!$banner) {
            return '';
        }

        return $banner->code;
    }

    public static function checkLimits($account) {
        $payment = Payment::query()
            ->with('Plan')
            ->where('enterprise_id', $account->enterprise_id)
            ->orderBy('id', 'desc')
            ->first();

        $limit = $payment->Plan->ShortItems->firstWhere('key', 'contas')->value;
        $current = 0;
        $accounts = Account::query()
            ->with(['Pages', 'Companies', 'Blogs'])
            ->where('enterprise_id', $account->enterprise_id)
            ->where('is_concurrent', 0)
            ->get();

        foreach($accounts as $account) {
            if ($account->social_network_id == 1) {
                $current += $account->Pages->filter(function($item) { return $item->in_use; })->count();
            }
            elseif($account->social_network_id == 4) {
                $current += $account->Companies->filter(function($item) { return $item->in_use; })->count();
            }
            elseif($account->social_network_id == 6) {
                $current += $account->Blogs->filter(function($item) { return $item->in_use; })->count();
            }
            else {
                $current++;
            }
        }

        return $current < $limit ? true : false;
    }

    public static function getRole($enterprise = null) {
        if (!$enterprise) {
            return [];
        }

        $enterprise = Auth::user()->Enterprises->firstWhere('slug', $enterprise);
        $roles = [];

        foreach($enterprise->TeamsUsers as $team_user) {
            if (Auth::user()->id == $team_user->User->site_user_id) {
                $roles[$team_user->team_id] = $team_user->role_id;
            }
        }

        return $roles;
    }

    public static function getWeekDay($strtotime = '', $full = false)
    {
        $m = '';
        $date = date('w');
        if (!empty($strtotime)) {
            $date = date('w', strtotime($strtotime));
        }

        switch(intval($date)) {
            case 0: {
                $m = 'Dom' . ($full ? 'ingo' : '');
                break;
            }
            case 1: {
                $m = 'Seg' . ($full ? 'unda-feira' : '');
                break;
            }
            case 2: {
                $m = 'Ter' . ($full ? 'ça-feira' : '');
                break;
            }
            case 3: {
                $m = 'Qua' . ($full ? 'rta-feira' : '');
                break;
            }
            case 4: {
                $m = 'Qui' . ($full ? 'nta-feira' : '');
                break;
            }
            case 5: {
                $m = 'Sex' . ($full ? 'ta-feira' : '');
                break;
            }
            case 6: {
                $m = 'Sáb' . ($full ? 'ado' : '');
                break;
            }            
            default:
                break;
        }
        return $m;
    }

    public static function getMonth($strtotime = '') {
        $m = '';
        $date = date('m');
        if (!empty($strtotime)) {
            $date = date('m', strtotime($strtotime));
        }

        switch(intval($date)) {
            case 1: {
                $m = 'janeiro';
                break;
            }
            case 2: {
                $m = 'fevereiro';
                break;
            }
            case 3: {
                $m = 'março';
                break;
            }
            case 4: {
                $m = 'abril';
                break;
            }
            case 5: {
                $m = 'maio';
                break;
            }
            case 6: {
                $m = 'junho';
                break;
            }
            case 7: {
                $m = 'julho';
                break;
            }
            case 8: {
                $m = 'agosto';
                break;
            }
            case 9: {
                $m = 'setembro';
                break;
            }
            case 10: {
                $m = 'outubro';
                break;
            }
            case 11: {
                $m = 'novembro';
                break;
            }
            case 12: {
                $m = 'dezembro';
                break;
            }
            default:
                break;
        }
        return $m;
    }

    public static function curl($url, $source) {
        //Log::info($source);
        //Log::info($url);

        $api = JFHApi::firstOrNew(['date' => date('Y-m-d')]);
        $api->hits = $api->hits + 1;
        $api->save();

        $key = 'c9b489d15emshe0bcb3d29f92b39p1a6597jsn2acd8e28fb62';
        $host = 'api-football-v1.p.rapidapi.com';

        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => [
                "X-RapidAPI-Host: " . $host,
                "X-RapidAPI-Key: " . $key
            ],
        ]);

        $response = curl_exec($curl);        

        curl_close($curl);

        return json_decode($response);
    }

    public static function get($url, $full_response = false) {
        $agents = array(
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.7; rv:7.0.1) Gecko/20100101 Firefox/7.0.1',
            'Mozilla/5.0 (X11; U; Linux i686; en-US; rv:*******) Gecko/20100508 SeaMonkey/2.0.4',
            'Mozilla/5.0 (Windows; U; MSIE 7.0; Windows NT 6.0; en-US)',
            'Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_7; da-dk) AppleWebKit/533.21.1 (KHTML, like Gecko) Version/5.0.5 Safari/533.21.1'
        );

        $ch = curl_init($url);
        curl_setopt($ch,CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_USERAGENT, $agents[array_rand($agents)]);
        $result = curl_exec($ch);
        $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if (!$full_response) {
            return $result;
        }

        return [
            'code' => $httpcode,
            'response' => $result
        ];
    }

    public static function getCurrentImagePath($filename) {
        $urls = ['https://kabist.com', 'https://verticalxp.com'];
        $return = '';

        foreach($urls as $url) {
            $ch = curl_init($url . '/uploads/' . $filename);
            curl_setopt($ch,CURLOPT_RETURNTRANSFER,1);
            curl_setopt($ch,CURLOPT_TIMEOUT,10);
            curl_exec($ch);
            $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpcode == 200) {
                $return = $url;
                break;
            }
        }

        return $return . '/uploads/' . $filename;
    }

    public static function getInfluencerPicture($id) {
        if (!$id) {
            $id = 1;
        }

        $influencer = KInfluencer::find($id);
        $disk = Storage::disk('public');
        $fullpath = $disk->getAdapter()->getPathPrefix() . $influencer->_avatar;

        $image = Image::make(public_path(env('APP_ROLE') . '/public/images/influencer_image_placeholder.jpg'));
        $image->text('@' . $influencer->_username, 185 + self::getOffset($influencer->_username), 200, function($font) {
            $font->file(public_path(env('APP_ROLE') . '/public/fonts/arial.ttf'));
            $font->size(16);
            $font->color('#333');
        });

        try {
            $avatar_image = Image::make($fullpath)->resize(70, 70);
            $avatar_image->mask(public_path(env('APP_ROLE') . '/public/images/mask.png'), true);

            $image->insert($avatar_image, 'top-left', 225, 90);

        }
        catch(\Exception $e) {

        }

        echo $image->encode('data-url');
        return false;
    }

    public static function getOffset($username) {
        return 0;
    }

    public static function getNumberHumanized($number) {
        return NumberHumanizer::metricSuffix($number);
    }

    public static function hasActiveClass($section, $isHome = false) {
        if ($isHome && url()->current() == $section) {
            return 'active';
        }

        return strpos(url()->current(), $section) !== false && !$isHome ? 'active' : '';
    }

    public static function getArticleImageSrc($article, $filename, $size, $webp = true, $vc = false) {
        if (isset($article->processed_symlink) && !empty($article->processed_symlink)) {
            $filename = $article->filename;
        }

        if ($webp) {
            $filename = str_replace('.jpg', '.jpg.webp', $filename);
        }

        if (env('APP_ENV') == 'local') {
            return asset('/uploads/' . $size. '_' . $filename);
        }

        if ($vc) {
            return 'https://vamoscomparar.com/uploads/' . $size . '_' .  $filename;
        }

        return 'https://verticalxp.com/uploads/' . $size . '_' .  $filename;
    }

    public static function getImageSrc($filename, $size, $webp = true) {
        if ($webp) {
            $filename = str_replace('.jpg', '.jpg.webp', $filename);
        }

        if (env('APP_ENV') == 'local') {
            return asset('/uploads/' . $size. '_' . $filename);
        }

        return 'https://verticalxp.com/uploads/' . $size . '_' .  $filename;
    }

    public static function getImageSrcV2($filename, $size = true, $webp = true) {
        if ($webp) {
            $filename = str_replace('.jpg', '.jpg.webp', $filename);
        }

        if (env('APP_ENV') == 'local') {
            return asset('/uploads/' . ($size ? ($size . '_') : '' ) . $filename);
        }

        return 'https://kabist.com/uploads/' . ($size ? ($size . '_') : '') . $filename;
    }

    public static function number_format_short($num) {
        if ($num > 1000) {
            $x = round($num);
            $x_number_format = number_format($x);
            $x_array = explode(',', $x_number_format);
            $x_parts = array('k', 'M', 'b', 't');
            $x_count_parts = count($x_array) - 1;
            $x_display = $x;
            $x_display = $x_array[0] . ((int) $x_array[1][0] !== 0 ? '.' . $x_array[1][0] : '');
            $x_display .= $x_parts[$x_count_parts - 1];

            return $x_display;

        }

        return $num;
    }

    public static function saveAvatar($url) {
        $path = false;

        try {
            $image = Image::make($url);

            if (!$image) {
                return false;
            }

            $image = $image->orientate()->fit(90, 90, function ($constraint) {
                $constraint->upsize();
            });

            $path = md5(uniqid()) . '.jpg';

            $disk = Storage::disk('public');
            $disk->put($path, $image->encode('jpg', 100), 'public');

            $base = env('APP_IMAGESPATH', storage_path('app/public/'));
            $source =  $base . $path;
            WebPConvert::convert($source, $source . '.webp', []);
        }
        catch(\Exception $e) {

        }

        return $path;
    }

    public static function getFixtureLink($fixture) {        
        if (isset($fixture->League)) {
            return action('JogosFutebolHoje\HomeController@index', [$fixture->League->slug, 'calendario', $fixture->id . '-' . $fixture->HomeTeam->slug . '-' . $fixture->AwayTeam->slug]);
        }

        return action('JogosFutebolHoje\HomeController@index', [$fixture->league->slug, 'calendario', $fixture->id . '-' . $fixture->home_team->slug . '-' . $fixture->away_team->slug]);
    }

    public static function getAge($bd) {
        $dob = new \DateTime($bd);
        $today = new \DateTime('today');

        return $dob->diff($today)->y;
    }

    public static function getDownloadLink($story) {
        $url = '';

        if ($story->media_type == 1) {
            foreach($story->image_versions2->candidates as $index => $image) {
                if ($index == 1) {
                    $url = $image->url;
                }
            }
        }
        elseif($story->media_type == 2) {
            foreach ($story->video_versions as $index => $video) {
                if ($index == 0) {
                    $url = $video->url;
                }
            }
        }    

        return route('download', ['type' => $story->media_type, 'url' => $url, 'id' => $story->id]);
    }

    public static function getMenu($categories) {
        $data = [];
        $n = 0;
        foreach($categories as $category) {
            if ($category->is_base) {
                $n++;

                $data []= [
                    'id' => $n,
                    'href' => '/noticias/' . $category->slug,
                    'name' => $category->name
                ];
            }
        }

        $n++;
        $news = [
            'id' => $n,
            'href' => '/noticias',
            'name' => 'Notícias',
            'type' => 'dropdown',
            'children' => []
        ];

        foreach($categories as $category) {
            if (!$category->is_base) {
                $n++;

                $news['children'] []= [
                    'id' => $n,
                    'href' => '/noticias/' . $category->slug,
                    'name' => $category->name
                ];
            }
        }

        $data []= $news;

        $n++;
        $tv = [
            'id' => $n,
            'href' => '/televisao',
            'name' => 'Televisão',
            'type' => 'dropdown',
            'children' => [                    
                [
                    'id' => $n + 1,
                    'href' => '/televisao/audiencias',
                    'name' => 'Audiências'
                ],
                [
                    'id' => $n + 2,
                    'href' => '/noticias/televisao/sic',
                    'name' => 'SIC'
                ],
                [
                    'id' => $n + 3,
                    'href' => '/noticias/televisao/tvi',
                    'name' => 'TVI'
                ]
            ]
        ];

        $data []= $tv;

        $influencer = [
            'id' => $n + 4,
            'href' => '/influenciadores',
            'name' => 'Influenciadores',
            'type' => 'dropdown',
            'children' => [                    
                [
                    'id' => $n + 5,
                    'href' => '/influenciadores/descobrir',
                    'name' => 'Descobrir'/*,
                    'type' => 'dropdown',
                    'children' => [
                        [
                            'id' => $n + 6,
                            'href' => '/influenciadores/descobrir/moda',
                            'name' => 'Moda'
                        ]
                    ]*/
                ],
                [
                    'id' => $n + 6,
                    'href' => '/influenciadores/ranking',
                    'name' => 'Ranking',
                ],
                [
                    'id' => $n + 7,
                    'href' => '/influenciadores/stories-em-anonimo',
                    'name' => 'Ver stories em anónimo',
                ],
                [
                    'id' => $n + 8,
                    'href' => '/influenciadores/giveaways',
                    'name' => 'Giveaways',
                ]
            ]
        ];
        
        $data []= $influencer;

        return $data;
    }

    public static function processStories($influencer) {
        $base = env('APP_IMAGESPATH', storage_path('app/public/'));
        $disk = Storage::disk('public');

        $hikerApi = new HikerApi();

        if ($result = $hikerApi->getStories($influencer->_id)) {            
            if ($result->status == 'ok') {
                if (!empty($result->reel)) {                        
                    if (!empty($result->reel->items)) {
                        foreach($result->reel->items as $item) {
                            $story = KInfluencerStory::firstOrNew(['_id' => $item->pk]);
                            $story->influencer_id = $influencer->id;
                            $story->posted_at = date('Y-m-d H:i:s', $item->taken_at);
                            $story->type = self::$medias_types[$item->media_type];

                            if (!empty($item->image_versions2) && empty($item->video_versions)) {
                                foreach ($item->image_versions2->candidates as $index => $image) {
                                    if (empty($index)) {
                                        $filename = md5($item->pk) . '.jpg';
                                        $disk->put($filename, file_get_contents($image->url), 'public');

                                        $story->thumbnail = $filename;
                                        $story->filename = $filename;
                                        $story->width = $image->width;
                                        $story->height = $image->height;

                                        break;
                                    }
                                }
                            }

                            if (!empty($item->video_versions)) {                
                                foreach ($item->video_versions as $index => $video) {
                                    if ($index == (sizeof($item->video_versions) - 1)) {
                                        $filename = md5($item->pk) . '.mp4';

                                        $disk->put($filename, file_get_contents($video->url), 'public');

                                        $story->filename = $filename;
                                        $story->width = $video->width;
                                        $story->height = $video->height;

                                        foreach ($item->image_versions2->candidates as $index => $image) {
                                            if (empty($index)) {
                                                if (empty($story->thumbnail)) {
                                                    $filename = md5($item->pk) . '.jpg';
                                                    $disk->put($filename, file_get_contents($image->url), 'public');
                                                    
                                                    $story->thumbnail = $filename;
                                                }
                                            }
                                        }

                                        break;
                                    }
                                }
                            }

                            $story->save();
                        }                            
                    }
                }

                KInfluencer::query()
                    ->where('id', $influencer->id)
                    ->update(['stories_scrapped_at' => date('Y-m-d H:i:s')]);
            }
        }

        $stories = KInfluencerStory::query()
            ->whereRaw('posted_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)')
            ->get();

        foreach($stories as $story) {            
            unlink($base . $story->filename);

            $story->forceDelete();
        }
    }

    private static function encryptMethodLength()
    {
        $number = filter_var('AES-256-CBC', FILTER_SANITIZE_NUMBER_INT);

        return intval(abs($number));
    }

    public static function decrypt($encryptedString) {
        $key = 'RNH';
        $json = json_decode(base64_decode($encryptedString), true);

        try {
            $salt = hex2bin($json["salt"]);
            $iv = hex2bin($json["iv"]);
        } catch (\Exception $e) {
            return null;
        }

        $cipherText = base64_decode($json['ciphertext']);

        $iterations = intval(abs($json['iterations']));
        if ($iterations <= 0) {
            $iterations = 999;
        }
        $hashKey = hash_pbkdf2('sha512', $key, $salt, $iterations, ( self::encryptMethodLength() / 4));
        unset($iterations, $json, $salt);

        $decrypted= openssl_decrypt($cipherText , 'AES-256-CBC', hex2bin($hashKey), OPENSSL_RAW_DATA, $iv);
        unset($cipherText, $hashKey, $iv);

        return $decrypted;
    }

    public static function crypt($string, $secret_key, $secret_iv, $action = 'e' ) {
        $output = false;
        $encrypt_method = "AES-256-CBC";
        $key = hash( 'sha256', $secret_key );
        $iv = substr( hash( 'sha256', $secret_iv ), 0, 16 );

        if( $action == 'e' ) {
            $output = base64_encode( openssl_encrypt( $string, $encrypt_method, $key, 0, $iv ) );
        }
        else if( $action == 'd' ){
            $output = openssl_decrypt( base64_decode( $string ), $encrypt_method, $key, 0, $iv );
        }

        return $output;
    }

    public static function PerplexityAPI($prompt) {
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => 'https://api.perplexity.ai/chat/completions',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_HTTPHEADER => [
                "Authorization: Bearer 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",
                "Content-Type: application/json"
            ],
            CURLOPT_POSTFIELDS => json_encode($prompt)
        ]);

        $response = json_decode(curl_exec($curl));
        $httpcode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);

        return [
            'response' => $response,
            'code' => $httpcode
        ];
    }
}