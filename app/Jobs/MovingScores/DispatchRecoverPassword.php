<?php

namespace App\Jobs\MovingScores;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Mail;

class DispatchRecoverPassword implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function handle()
    {
        config([
            'mail.driver' => 'smtp',
            'mail.host' => 'smtp.migadu.com',
            'mail.port' => '465',
            'mail.from.name' => 'MovingScores',
            'mail.from.address' => '<EMAIL>',
            'mail.encryption' => 'ssl',
            'mail.username' => '<EMAIL>',
            'mail.password' => 'Scripting#84_'
        ]);

        Mail::send('auth.passwords.reset', ['token' => $this->data['token']], function($message) {
            $message->to($this->data['email']);
            $message->subject('Recuperar palavra-chave');
        });
    }
}
