<?php

namespace App\Console;

use App\Console\Commands\AddCategoriesSlugsAndImages;
use App\Console\Commands\AddingMoreDetailsToFollowers;
use App\Console\Commands\BetsOptimizer\CheckPredictions;
use App\Console\Commands\BetsOptimizer\GetLiveScoreTeamsId;
use App\Console\Commands\BetsOptimizer\GetPredictions;
use App\Console\Commands\BetsOptimizer\LiveScoreAPIAlert;
use App\Console\Commands\BetsOptimizer\SendDailyPredictions;
use App\Console\Commands\BetsOptimizer\UpdateSupportTables;
use App\Console\Commands\CheckBetsResults;
use App\Console\Commands\CheckIfUsersFollowedBack;
use App\Console\Commands\CheckSitesStatus;
use App\Console\Commands\ConvertJpgSiteArticlesImagesToWebp;
use App\Console\Commands\CreateCrudElements;
use App\Console\Commands\CreateSymlinksForSiteArticlesImages;
use App\Console\Commands\GetGender;
use App\Console\Commands\GrabFishStores;
use App\Console\Commands\GrabSitesInfluencersFollowers;
use App\Console\Commands\JogosFutebolHoje\GetLastNews;
use App\Console\Commands\JogosFutebolHoje\SyncTweets;
use App\Console\Commands\Kabist\AddInfluencer;
use App\Console\Commands\Kabist\AddInfluencersInQueue;
use App\Console\Commands\Kabist\ChangePassword;
use App\Console\Commands\Kabist\CheckCompetitorsPublication;
use App\Console\Commands\Kabist\CheckCompetitorsPublication_FB;
use App\Console\Commands\Kabist\CheckGender;
use App\Console\Commands\Kabist\CheckLinkedInAccountStatus;
use App\Console\Commands\Kabist\CheckPayments;
use App\Console\Commands\Kabist\DoPublications;
use App\Console\Commands\Kabist\DoReposts;
use App\Console\Commands\Kabist\DoStats;
use App\Console\Commands\Kabist\GetContestsWinners;
use App\Console\Commands\Kabist\GetPayments;
use App\Console\Commands\Kabist\HandleCampaignBegin;
use App\Console\Commands\Kabist\HandleCampaignResults;
use App\Console\Commands\Kabist\ProcessFeedEmojis;
use App\Console\Commands\Kabist\RecreateImagesSizesForFeed;
use App\Console\Commands\Kabist\SaveInfluencer;
use App\Console\Commands\Kabist\SaveInfluencerFeed;
use App\Console\Commands\Kabist\SaveInfluencerNumber;
use App\Console\Commands\Kabist\SaveInfluencersScore;
use App\Console\Commands\Kabist\DeleteImagesOlderThan4Months;
use App\Console\Commands\LivescoreApiEveryMinuteAnalyser;
use App\Console\Commands\Maisviagens\GrabTravels;
use App\Console\Commands\Million\ScrapEuroMillionsSite;
use App\Console\Commands\Million\ScrapMillionInfo;
use App\Console\Commands\MoveFollowersToSiteFollowers;
use App\Console\Commands\NasRedesSociais\GrabAudiences;
use App\Console\Commands\PopulatePlayers;
use App\Console\Commands\RandomStartFollowingUsers;
use App\Console\Commands\SyncLiveScoreTeamNames;
use App\Console\Commands\SyncPlacardEvents;
use App\Console\Commands\SyncResults;
use App\Console\Commands\TestIGDM;
use App\Console\Commands\TestTwitterDM;
use App\Console\Commands\UpdateDeploy;
use App\Console\Commands\Voom\SetAirportsImagesAsWebp;
use App\Console\Commands\Zaptian\ListenForHashtagIG;
use App\Console\Commands\Zaptian\ListenForHashtagTwitter;
use App\Console\Commands\AddMissingLiveScoreTeamID;
use App\Console\Commands\ApostasDePescada\GetTeamsColor;
use App\Console\Commands\CapasJornaisHoje\GetCovers;
use App\Console\Commands\JogosFutebolHoje\AddBetJFH;
use App\Console\Commands\JogosFutebolHoje\AddBookmakersJFH;
use App\Console\Commands\JogosFutebolHoje\AddCoachesJFH;
use App\Console\Commands\JogosFutebolHoje\AddCountriesJFH;
use App\Console\Commands\JogosFutebolHoje\AddFixturesEventsJFH;
use App\Console\Commands\JogosFutebolHoje\AddFixturesH2H;
use App\Console\Commands\JogosFutebolHoje\AddFixturesJFH;
use App\Console\Commands\JogosFutebolHoje\AddFixturesLineups;
use App\Console\Commands\JogosFutebolHoje\AddFixturesOddsJFH;
use App\Console\Commands\JogosFutebolHoje\AddFixturesPredictionsJFH;
use App\Console\Commands\JogosFutebolHoje\AddFixturesStats;
use App\Console\Commands\JogosFutebolHoje\AddLeaguesJFH;
use App\Console\Commands\JogosFutebolHoje\AddPlayersJFH;
use App\Console\Commands\JogosFutebolHoje\AddSeasonsJFH;
use App\Console\Commands\JogosFutebolHoje\AddSquadsJFH;
use App\Console\Commands\JogosFutebolHoje\AddStandingsJFH;
use App\Console\Commands\JogosFutebolHoje\AddTeamsJFH;
use App\Console\Commands\JogosFutebolHoje\AddTeamStatsJFH;
use App\Console\Commands\JogosFutebolHoje\CheckFixturesFinalResult;
use App\Console\Commands\JogosFutebolHoje\ConverImagesOfLeaguesAndTeams;
use App\Console\Commands\JogosFutebolHoje\SyncPlacardEvents as NewSyncPlacardEvents;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use App\Console\Commands\VamosComparar\Sites\Worten;
use App\Console\Commands\VamosComparar\Sites\RadioPopular;
use App\Console\Commands\VamosComparar\Sites\MediaMarkt;
use App\Console\Commands\VamosComparar\Sites\Fnac;
use App\Console\Commands\VamosComparar\SeedFuelsTable;
use App\Console\Commands\VamosComparar\GetFuelsPrices;
use App\Console\Commands\JogosFutebolHoje\GetGoalsFromTwitter;
use App\Console\Commands\JogosFutebolHoje\JFHGrabPlacardTeamsQuery;
use App\Console\Commands\JogosFutebolHoje\YoutubeVideos;
use App\Console\Commands\JogosFutebolHoje\GetGoalsFromTwitterNew;
use App\Console\Commands\JogosFutebolHoje\SyncTudoNumClickEvents;
use App\Console\Commands\JogosFutebolHoje\UpdateFixtures;
use App\Console\Commands\NasRedesSociais\TestUploadSpaces;
use App\Console\Commands\NasRedesSociais\UpdateInfluencers;
use App\Console\Commands\NasRedesSociais\UpdateInfluencersStories;
use App\Console\Commands\ResultadosNaHora\CalculateFeaturedGames;
use App\Console\Commands\ResultadosNaHora\GenerateTeamsLeaguesAndCountriesMedias;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        CheckIfUsersFollowedBack::class,
        GrabSitesInfluencersFollowers::class,
        RandomStartFollowingUsers::class,
        ListenForHashtagIG::class,
        ListenForHashtagTwitter::class,
        GrabTravels::class,
        CheckSitesStatus::class,
        SyncResults::class,
        SyncPlacardEvents::class,
        LivescoreApiEveryMinuteAnalyser::class,
        SyncLiveScoreTeamNames::class,
        PopulatePlayers::class,
        CheckBetsResults::class,
        AddingMoreDetailsToFollowers::class,
        TestIGDM::class,
        GetGender::class,
        TestTwitterDM::class,
        UpdateDeploy::class,
        GetPredictions::class,
        CheckPredictions::class,
        CheckBetsResults::class,
        SendDailyPredictions::class,
        GetLiveScoreTeamsId::class,
        UpdateSupportTables::class,
        MoveFollowersToSiteFollowers::class,
        \App\Console\Commands\BetsOptimizer\GetPayments::class,
        CreateCrudElements::class,
        CheckPayments::class,
        CheckCompetitorsPublication::class,
        CheckCompetitorsPublication_FB::class,
        DoPublications::class,
        DoStats::class,
        GetPayments::class,
        SaveInfluencerFeed::class,
        SaveInfluencerNumber::class,
        HandleCampaignResults::class,
        HandleCampaignBegin::class,
        GetContestsWinners::class,
        CheckLinkedInAccountStatus::class,
        CheckGender::class,
        DoReposts::class,
        AddInfluencer::class,
        ChangePassword::class,
        SaveInfluencer::class,
        SaveInfluencersScore::class,
        AddInfluencersInQueue::class,
        LiveScoreAPIAlert::class,
        GetLastNews::class,
        ConvertJpgSiteArticlesImagesToWebp::class,
        SyncTweets::class,
        ProcessFeedEmojis::class,
        RecreateImagesSizesForFeed::class,
        DeleteImagesOlderThan4Months::class,
        GrabAudiences::class,
        GrabFishStores::class,
        AddCategoriesSlugsAndImages::class,
        CreateSymlinksForSiteArticlesImages::class,
        AddMissingLiveScoreTeamID::class,
        ScrapEuroMillionsSite::class,
        ScrapMillionInfo::class,
        Worten::class,
        RadioPopular::class,
        MediaMarkt::class,
        Fnac::class,
        SeedFuelsTable::class,
        GetFuelsPrices::class,
        SetAirportsImagesAsWebp::class,
        GetCovers::class,
        AddCountriesJFH::class,
        AddSeasonsJFH::class,
        AddLeaguesJFH::class,
        AddCoachesJFH::class,
        AddPlayersJFH::class,
        AddStandingsJFH::class,
        AddFixturesJFH::class,
        AddFixturesH2H::class,
        AddFixturesEventsJFH::class,
        AddFixturesLineups::class,
        AddFixturesStats::class,
        AddTeamsJFH::class,
        AddBookmakersJFH::class,
        AddBetJFH::class,
        AddFixturesOddsJFH::class,
        NewSyncPlacardEvents::class,
        JFHGrabPlacardTeamsQuery::class,
        ConverImagesOfLeaguesAndTeams::class,
        YoutubeVideos::class,
        GetGoalsFromTwitterNew::class,
        SyncTudoNumClickEvents::class,
        UpdateInfluencers::class,
        UpdateInfluencersStories::class,
        TestUploadSpaces::class,
        AddSquadsJFH::class,
        AddTeamStatsJFH::class,
        AddFixturesPredictionsJFH::class,
        CalculateFeaturedGames::class,
        GenerateTeamsLeaguesAndCountriesMedias::class,
        UpdateFixtures::class,
        GetTeamsColor::class,
        CheckFixturesFinalResult::class
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        if (env('APP_ROLE') == 'verticalxp.com') {
            $schedule
                ->command('queue:work --tries=1') /* queue:work --tries=10 --daemon */
                ->everyMinute()
                ->withoutOverlapping();

            // $schedule->command('maisviagens:grab-travels')->cron('0 */6 * * *');

            $schedule->command('grab:followers')->cron('15 */4 * * *');

            $schedule->command('grab:details')->cron('45 */2 * * *');

            $schedule->command('grab:sites_followers')->dailyAt('02:00');

            $schedule->command('check:followed_back')->hourlyAt(0);

            $schedule->command('start:following')->twiceDaily(8, 20);

            $schedule->command('check:sites_status')->everyFifteenMinutes();

            $schedule->command('sync:events')->cron('0 */4 * * *');

            $schedule->command('sync:results')->dailyAt('07:00');

            // $schedule->command('get:genders')->hourlyAt(50); @TODO: refactured this

            // $schedule->command('zaptian:instagram-listen-for-hash-tags')->hourly();

            $schedule->command('get:predictions')->everyFiveMinutes();

            $schedule->command('check:predictions')->dailyAt('07:30');

            $schedule->command('check:results')->hourlyAt(45);

            // $schedule->command('send:predictions')->dailyAt('10:00');

            // $schedule->command('uploads:generate')->dailyAt('23:00');
            // $schedule->command('uploads:upload')->everyThirtyMinutes();
            // $schedule->command('sync:livescore')->everyMinute();
            // $schedule->command('update:support_tables')->dailyAt('10:30');
            // $schedule->command('move:followers')->dailyAt('03:00');
            // $schedule->command('get:payments')->cron('*/2 * * * *');
            // $schedule->command('kabist:check_payments')->dailyAt('00:01');
            // $schedule->command('kabist:check_competitors_publications')->cron('0 */12 * * *');
            // $schedule->command('kabist:check_competitors_publications_fb')->cron('30 */4 * * *');
            // $schedule->command('kabist:stats')->cron('15 */4 * * *');
            // $schedule->command('kabist:publish')->everyFifteenMinutes();
            // $schedule->command('kabist:update_accounts_audience')->dailyAt('08:00');
            // $schedule->command('kabist:get_payments')->cron('*/2 * * * *');
            // $schedule->command('kabist:handle_campaign_stats')->cron('0 */4 * * *');
            // $schedule->command('kabist:handle_campaign_begin')->dailyAt('00:01');
            // $schedule->command('kabist:get_contests_winners')->everyFifteenMinutes();
            // $schedule->command('kabist:check_linkedin_account_status')->everyThirtyMinutes();
            // $schedule->command('kabist:check_gender')->hourlyAt(55);
            // $schedule->command('kabist:reposts')->everyFifteenMinutes();

            $schedule->command('alert:livescore')->dailyAt('07:30');

            $schedule->command('recreate:images_sizes')->everyFifteenMinutes();

            $schedule->command('nrs:get_audiences')->cron('0 */2 * * *');

            $schedule->command('symliks:create')->twiceDaily();

            $schedule->command('million:scrap-results')->hourly();

            $schedule->command('million:scrap-million')->hourlyAt(5);            

            $schedule->command('jfh:get_bets')->cron('0 0 1 * *'); /* every month day 1 hour 0 */
            $schedule->command('jfh:get_bookmakers')->cron('0 1 1 * *'); /* every month day 1 hour 1 */
            $schedule->command('jfh:get_countries')->cron('0 2 1 * *'); /* every month day 1 hour 2 */
            $schedule->command('jfh:get_coaches')->hourlyAt(22); ; /* every week day 0 */
            $schedule->command('jfh:get_fixtures_events')->everyMinute();
            $schedule->command('jfh:get_h2h')->everyFifteenMinutes();
            $schedule->command('jfh:get_fixtures')->everyFifteenMinutes();
            $schedule->command('jfh:update_fixtures')->everyMinute();
            $schedule->command('jfh:get_lineups')->everyFifteenMinutes();
            $schedule->command('jfh:get_fixtures_odds')->everyFifteenMinutes();        
            $schedule->command('jfh:get_stats')->everyMinute();            
            $schedule->command('jfh:get_leagues')->cron('0 3 1 * *'); /* every month day 1 hour 3 */
            $schedule->command('jfh:get_players')->hourlyAt(44);
            $schedule->command('jfh:get_seasons')->cron('30 3 1 */3 *'); /* Every 3 months */
            $schedule->command('jfh:get_standings')->hourly();
            $schedule->command('jfh:sync_events')->hourly();            
            $schedule->command('jfh:get_youtube_videos')->dailyAt('09:30');
            $schedule->command('jfh:get_squads')->weekly();
            $schedule->command('jfh:get_standings_history')->daily();
            $schedule->command('jfh:get_leagues_rounds')->daily();
            $schedule->command('jfh:check_final_result')->everyFiveMinutes();

            // $schedule->command('get:goals-new')->everyFiveMinutes();

            $schedule->command('get:goals')->cron("0 0,1,2,12,13,14,15,16,17,18,19,20,21,22,23 * * *");
        }
        elseif (env('APP_ROLE') == 'jogosfutebolhoje.pt') {
            $schedule->command('news:get_last')->hourlyAt(30);

            //$schedule->command('sync:tweets')->hourlyAt(45);            
            
            $schedule->command('jfh:sync_events_tnc')->dailyAt('09:25');

            // $schedule->command('jfh:convert_images')->everyFiveMinutes();
        }
        elseif (env('APP_ROLE') == 'kabist.com') {
            //$schedule->command('kabist:check_feeds')->dailyAt('01:' . sprintf('%02d', rand(1, 30)));

            //$schedule->command('kabist:save_influencers')->hourly();

            //$schedule->command('process:emojis')->daily();

            //$schedule->command('kabist:delete_older_images')->monthly();
        }
        elseif (env('APP_ROLE') == 'api.capasjornaishoje.pt') {
            $schedule->command('get:covers')->cron('0 6-9 * * *');
        }
        elseif (env('APP_ROLE') == 'api.nas-redes-sociais.com') {
            //$schedule->command('nrs:update-influencers')->everyFiveMinutes();
            $schedule->command('nrs:save-influencers-score')->dailyAt('01:30');
        }
        elseif (env('APP_ROLE') == 'vamoscomparar.com') {
            $schedule->command('fuels:prices')->weeklyOn(1, '2:00');
            $schedule->command('fuels:seed')->daily();        
        }
        elseif (env('APP_ROLE') == 'api.apostasdepescada.com') {
            $schedule->command('jfh:get_predictions')->hourly();
            $schedule->command('get:pre-game-facts')->hourly();
        }
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
