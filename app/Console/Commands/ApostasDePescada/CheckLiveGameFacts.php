<?php

namespace App\Console\Commands\ApostasDePescada;

use App\Helpers\Utils;
use App\Libs\HandleCache\HandleCache;
use App\Models\JFHFact;
use App\Models\JFHFixture;
use App\Models\JFHLiveOdd;
use App\Models\JFHLiveOddOdd;
use App\Models\JFHLiveOddOddValue;
use App\Models\JFHSeason;
use App\Models\JFHStanding;
use Illuminate\Console\Command;

class CheckLiveGameFacts extends Command
{
    protected $signature = 'get:live-game-facts';

    protected $description = 'Command description';

    private $facts = [
        'GOAL' => '', // every minute
        'RED_CARD' => '', // every minute
        'HIGH_VOLUME_YELLOW_CARDS' => '', // nos ultimos 10m
        'HIGH_VOLUME_BALL_POSSESSION' => '', // nos ultimos 10m
        'HIGH_VOLUME_SHOTS' => '', // nos ultimos 10m
        'LOW_VOLUME_SHOTS' => '', // nos ultimos 20m
        'HIGH_VOLUME_CORNERS' => '', // nos ultimos 10m
        'HiGH_VOLUME_GOALS' => '', // nos ultimos 15m
        'HT' => 'Algumas análises ao interval',
        'ZERO_ZERO_RESULT' => 'Corre de 15 em 15 minutos para ver se o resultado está 0-0',
        '15_MINUTES_TO_HT' => 'Corre a 15 do fim da 1ª parte e vê se as equipas estão empatadas',
        '30_MINUTES_TO_FT' => 'Corre a 30 do fim e vê se as equipas estão empatadas',
        '15_MINUTES_TO_FT' => 'Corre a 30 do fim e vê se as equipas estão empatadas',
        'ODDS' => '' // every minute
    ];

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $json = Utils::curl("https://api-football-v1.p.rapidapi.com/v3/fixtures?live=all", 'GetLiveFixtures');

        if (!isset($json->response)) {
            return;
        }

        $response = $json->response;

        $fixturesIds = array_map(function ($item) {
            return $item->fixture->id;
        }, $response);

        $season = JFHSeason::query()
            ->where('is_current', 1)
            ->first();

        $lastThreeSeasons = JFHSeason::query()
            ->where('id', '<=', $season->id)
            ->orderBy('year', 'desc')
            ->limit(3)
            ->get();

        $fixtures = JFHFixture::query()
            ->with(['HomeTeam', 'AwayTeam', 'Odds', 'Events', 'Stats'])            
            ->whereIn('api_id', $fixturesIds)
            //->where('id', 23666)
            ->get();

        foreach ($fixtures as $fixture) {
            $fixtureData = $this->getFixtureFromResponse($response, $fixture->api_id);

            // $fixtureStats = $this->getStats($fixture);

            if (!$fixtureData) {
                continue;
            }

            $elapsed = $fixtureData->fixture->status->elapsed;
            $status = $fixtureData->fixture->status->short;
            $homeGoals = $fixtureData->goals->home;
            $awayGoals = $fixtureData->goals->away;
            $score = $homeGoals . '-' . $awayGoals;

            $this->handleOdds($fixture);

            $favoriteTeam = $this->getFavoriteTeam($fixture);

            $fixtureLive = $this->isFixtureLive($status);

            foreach ($this->facts as $fact => $value) {
                switch ($fact) {
                    case 'GOAL': { // verificação a cada minute
                        if ($fixtureLive) {
                            $fixtureGoalsEvents = $this->getFixtureDataLiveEvents($fixtureData, 'Goal', 'Missed Penalty');

                            if ($fixtureGoalsEvents->count() > 0) {                                
                                $this->inactiveOldRecords($fixture, ['ZERO_ZERO_RESULT']);

                                $scoresData = $fixtureGoalsEvents->map(function($event) use ($fixture) {
                                    return $event->team->id == $fixture->HomeTeam->api_id ? $fixture->home_team_id : $fixture->away_team_id;
                                })->toArray();

                                $lastGoalEventTeamId = collect($fixtureGoalsEvents)->sortByDesc(function($event) {
                                    return intval($event->time->elapsed) + ($event->time->extra ?? 0);
                                })->first()->team->id == $fixture->HomeTeam->api_id ? $fixture->home_team_id : $fixture->away_team_id; 
                                
                                $scoresData = array_map(function ($item) use ($lastGoalEventTeamId) {
                                    return $item == $lastGoalEventTeamId ? $item : null;
                                }, $scoresData);

                                if (in_array($score, $this->getScores())) {
                                    $this->inactiveOldRecords($fixture, 'GOAL');

                                    $this->handleScores($score, $scoresData, $fixture, $elapsed, $lastGoalEventTeamId, $favoriteTeam, $lastThreeSeasons);                                    
                                }
                            }
                        }

                        break;
                    }
                    case 'RED_CARD': {
                        if ($fixtureLive) {
                            $redCards = $this->getFixtureDataLiveEvents($fixtureData, 'Card', 'Red Card', '==');

                            if ($redCards->count() > 0) {
                                $this->inactiveOldRecords($fixture, 'RED_CARD');

                                $redCardsHomeCount = $redCards->filter(function ($event) use ($fixture) {
                                    return $event->team->id == $fixture->HomeTeam->api_id;
                                })->count();

                                $redCardsAwayCount = $redCards->filter(function ($event) use ($fixture) {
                                    return $event->team->id == $fixture->AwayTeam->api_id;
                                })->count();

                                $cardsData = $redCards->map(function($event) use ($fixture) {
                                    return $event->team->id == $fixture->HomeTeam->api_id ? $fixture->home_team_id : $fixture->away_team_id;
                                })->toArray();

                                $lastTeamIdGotCard = collect($redCards)->sortByDesc(function($event) {
                                    return intval($event->time->elapsed) + ($event->time->extra ?? 0);
                                })->first()->team->id == $fixture->HomeTeam->api_id ? $fixture->home_team_id : $fixture->away_team_id; 

                                $cardsData = array_map(function ($item) use ($lastTeamIdGotCard) {
                                    return $item == $lastTeamIdGotCard ? $item : null;
                                }, $cardsData);

                                $score = $redCardsHomeCount . '-' . $redCardsAwayCount;

                                if (in_array($score, $this->getRedCards())) {
                                    $this->handleRedCards($score, $cardsData, $fixture, $lastTeamIdGotCard, $elapsed);
                                }
                            }
                        }

                        break;
                    }
                    case 'HIGH_VOLUME_YELLOW_CARDS': {
                        if ($fixtureLive) {
                            if ($this->isInTime($elapsed, 10)) {
                                $yellowCards = $this->getFixtureDataLiveEvents($fixtureData, 'Card', 'Yellow Card', '==')->filter(function ($event) use ($elapsed) {
                                    return $event->time->elapsed >= ($elapsed - 10);
                                });

                                if (sizeof($yellowCards) >= 0) {
                                    $this->inactiveOldRecords($fixture, 'HIGH_VOLUME_YELLOW_CARDS');

                                    foreach([$fixture->home_team_id, $fixture->away_team_id] as $teamId) {
                                        $games = $this->getFixturesForATeamInCurrentSeason($fixture, $teamId);                                        

                                        $totalGames = $games->count();

                                        if ($totalGames > 0) {
                                            $cards = $games->flatMap(function($game) use ($teamId) {
                                                return $game->Stats
                                                    ->where('team_id', $teamId)
                                                    ->where('type', 'Yellow Cards');
                                            })->sum('value');

                                            $cardsAvg = $cards / $totalGames;
                                            $cardsAvgPerTenMinutes = $cardsAvg / 9;

                                            $currentStats = $fixture->Stats
                                                ->where('team_id', $teamId)
                                                ->where('type', 'Yellow Cards');

                                            if ($currentStats) {
                                                $cardsLastTenMinutes = $currentStats->sum('extra');

                                                if ($cardsLastTenMinutes > ($cardsAvgPerTenMinutes * 2)) {
                                                    $fact = JFHFact::firstOrNew(['fixture_id' => $fixture->id, 'team_id' => $teamId, 'fact' => $fact, 'type' => 'HIGH_VOLUME_YELLOW_CARDS', 'is_live' => 1, 'elapsed' => $elapsed]);
                                                    $fact->description = sprintf('A equipa tem um volume de cartões amarelos muito alto! Já tiveram %d cartões amarelos nos últimos 10 minutos, o que é mais do que o dobro da média de (%.2f) cartões por intervalo de 10 minutos nos jogos desta época.', $cardsLastTenMinutes, $cardsAvgPerTenMinutes);
                                                    $fact->save();
                                                }
                                            }
                                        }
                                    }
                                }                                                        
                            }
                        }

                        break;
                    }
                    case 'HIGH_VOLUME_BALL_POSSESSION': {
                        if ($fixtureLive) {
                            if ($this->isInTime($elapsed, 10)) {
                                $this->inactiveOldRecords($fixture, 'HIGH_VOLUME_BALL_POSSESSION');

                                foreach([$fixture->home_team_id, $fixture->away_team_id] as $teamId) {
                                    $games = $this->getFixturesForATeamInCurrentSeason($fixture, $teamId);                                    

                                    $totalGames = $games->count();

                                    if ($totalGames > 0) {
                                        $ballPossession = $games->flatMap(function($game) use ($teamId) {
                                            return $game->Stats
                                                ->where('team_id', $teamId)
                                                ->where('type', 'Ball Possession');
                                        })->sum('value');

                                        $ballPossessionAvg = $ballPossession / $totalGames;
                                        $ballPossessionAvgPerTenMinutes = $ballPossessionAvg / 9;

                                        $currentStats = $fixture->Stats
                                                ->where('team_id', $teamId)
                                                ->where('type', 'Ball Possession');

                                        if ($currentStats) {
                                            $ballPossessionLastTenMinutes = $currentStats->sum('extra');

                                            if ($ballPossessionLastTenMinutes > $ballPossessionAvgPerTenMinutes) {
                                                $fact = JFHFact::firstOrNew(['fixture_id' => $fixture->id, 'team_id' => $teamId, 'fact' => $fact, 'type' => 'HIGH_VOLUME_BALL_POSSESSION', 'is_live' => 1, 'elapsed' => $elapsed]);
                                                $fact->description = sprintf('A equipa tem uma posse de bola muito alta! Tiveram %d%% de posse de bola nos últimos 10 minutos, o que é um valor mais alto do que a média de (%.2f%%) por intervalo de 10 minutos nos jogos desta época.', $ballPossessionLastTenMinutes, $ballPossessionAvgPerTenMinutes);
                                                $fact->save();
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        break;
                    }
                    case 'LOW_VOLUME_SHOTS': {
                        // @TODO:

                        break;
                    }
                    case 'HIGH_VOLUME_SHOTS': {
                        if ($fixtureLive) {
                            if ($this->isInTime($elapsed, 10)) {
                                $this->inactiveOldRecords($fixture, 'HIGH_VOLUME_SHOTS');

                                foreach([$fixture->home_team_id, $fixture->away_team_id] as $teamId) {
                                    $games = $this->getFixturesForATeamInCurrentSeason($fixture, $teamId);

                                    $totalGames = $games->count();

                                    if ($totalGames > 0) {
                                        $shots = $games->flatMap(function($game) use ($teamId) {
                                            return $game->Stats
                                                ->where('team_id', $teamId)
                                                ->where('type', 'Total Shots');
                                        })->sum('value');

                                        $shotsAvg = $shots / $totalGames;
                                        $shotsAvgPerTenMinutes = $shotsAvg / 9;

                                        $currentStats = $fixture->Stats
                                                ->where('team_id', $teamId)
                                                ->where('type', 'Total Shots');

                                        if ($currentStats) {
                                            $shotsLastTenMinutes = $currentStats->sum('extra');

                                            if ($shotsLastTenMinutes > $shotsAvgPerTenMinutes) {
                                                $fact = JFHFact::firstOrNew(['fixture_id' => $fixture->id, 'team_id' => $teamId, 'fact' => $fact, 'type' => 'HIGH_VOLUME_SHOTS', 'is_live' => 1, 'elapsed' => $elapsed]);
                                                $fact->description = sprintf('A equipa tem um volume de remates muito alto! Já tiveram %d remates nos últimos 10 minutos, o que é mais do que a média de (%.2f) remates por intervalo de 10 minutos nos jogos desta época.', $shotsLastTenMinutes, $shotsAvgPerTenMinutes);
                                                $fact->save();
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        break;
                    }
                    case 'HIGH_VOLUME_CORNERS': {
                        if ($fixtureLive) {
                            if ($this->isInTime($elapsed, 10)) {
                                $this->inactiveOldRecords($fixture, 'HIGH_VOLUME_CORNERS');

                                foreach([$fixture->home_team_id, $fixture->away_team_id] as $teamId) {
                                    $games = $this->getFixturesForATeamInCurrentSeason($fixture, $teamId);                                    

                                    $totalGames = $games->count();

                                    if ($totalGames > 0) {
                                        $corners = $games->flatMap(function($game) use ($teamId) {
                                            return $game->Stats
                                                ->where('team_id', $teamId)
                                                ->where('type', 'Corner Kicks');
                                        })->sum('value');

                                        $cornersAvg = $corners / $totalGames;
                                        $cornersAvgPerTenMinutes = $cornersAvg / 9;

                                        $currentStats = $fixture->Stats
                                                ->where('team_id', $teamId)
                                                ->where('type', 'Corner Kicks');

                                        if ($currentStats) {
                                            $cornersLastTenMinutes = $currentStats->sum('extra');

                                            if ($cornersLastTenMinutes > $cornersAvgPerTenMinutes) {
                                                $fact = JFHFact::firstOrNew(['fixture_id' => $fixture->id, 'team_id' => $teamId, 'fact' => $fact, 'type' => 'HIGH_VOLUME_CORNERS', 'is_live' => 1, 'elapsed' => $elapsed]);
                                                $fact->description = sprintf('A equipa tem um volume de cantos muito alto! Já tiveram %d cantos nos últimos 10 minutos, o que é mais do que a média de (%.2f) cantos por intervalo de 10 minutos nos jogos desta época.', $cornersLastTenMinutes, $cornersAvgPerTenMinutes);
                                                $fact->save();
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        break;
                    }
                    case 'HiGH_VOLUME_GOALS': {
                        if ($fixtureLive) {
                            if ($this->isInTime($elapsed, 15)) {
                                $this->inactiveOldRecords($fixture, 'HiGH_VOLUME_GOALS');

                                foreach([$fixture->home_team_id, $fixture->away_team_id] as $teamId) {
                                    $teamApiId = $teamId == $fixture->home_team_id ? $fixture->HomeTeam->api_id : $fixture->AwayTeam->api_id;

                                    $games = $this->getFixturesForATeamInCurrentSeason($fixture, $teamId);

                                    $totalGames = $games->count();
                                    $totalGoals = $games->sum(function($game) use ($teamId) {
                                        return $game->home_team_id == $teamId ? $game->ft_home_goals : $game->ft_away_goals;
                                    });

                                    $goalsAvg = $totalGoals / $totalGames;
                                    $goalsAvgPerFifteenMinutes = $goalsAvg / 6;

                                    $goalsEvents = $this->getFixtureDataLiveEvents($fixtureData, 'Goal', 'Missed Penalty', '!=')->filter(function ($event) use ($elapsed, $teamApiId) {
                                        return $event->time->elapsed >= ($elapsed - 10) && $event->team->id == $teamApiId;
                                    });

                                    if ($goalsEvents->count() > ($goalsAvgPerFifteenMinutes * 2)) {
                                        $fact = JFHFact::firstOrNew(['fixture_id' => $fixture->id, 'team_id' => $teamId, 'fact' => $fact, 'type' => 'HiGH_VOLUME_GOALS', 'is_live' => 1, 'elapsed' => $elapsed]);
                                        $fact->description = sprintf('A equipa tem um volume de golos alto! Já tiveram %d golos nos últimos 15 minutos, o que é mais do dobro da média de (%.2f) golos por intervalo de 15 minutos nos jogos desta época.', $goalsEvents->count(), $goalsAvgPerFifteenMinutes);
                                        $fact->save();
                                    }
                                }
                            }
                        }

                        break;
                    }
                    case 'ODDS': {
                        if ($fixtureLive && $favoriteTeam) {
                            $this->inactiveOldRecords($fixture, 'ODDS');

                            $matchWinnerBetData = $fixture->Odds
                                ->flatMap->Bets
                                ->firstWhere('Bet.name', 'Match Winner');

                            $liveOdd = JFHLiveOdd::query()
                                    ->with(['Odds', 'Odds.Values'])
                                    ->whereHas('Odds', function($q) {
                                        $q->where('name', 'Fulltime Result');
                                    })
                                    ->where('fixture_id', $fixture->id)
                                    ->first();

                            if ($matchWinnerBetData && $liveOdd) {
                                $currentMatchWinnerBet = $liveOdd->Odds->firstWhere('name', 'Fulltime Result')->Values->map(function ($value) {
                                    return ['value' => $value->value, 'odd' => $value->odd];
                                });

                                $balanced = true;
                                foreach($currentMatchWinnerBet as $item) {
                                    $percentage = (1 / $item->odd) * 100;

                                    if ($percentage > 70) {
                                        $balanced = false;
                                        break;
                                    }
                                }

                                if ($balanced) {
                                    $fact = JFHFact::firstOrNew(['fixture_id' => $fixture->id, 'team_id' => $favoriteTeam, 'fact' => $fact, 'type' => 'ODDS', 'is_live' => 1, 'elapsed' => $elapsed]);
                                    $fact->description = 'As odds estão equilibradas! A equipa ' . $this->getTeamNameById($fixture, $favoriteTeam) . ' era favorita a vencer à partida para ganhar o jogo mas, de momento, nenhum resultado tem uma probabilidade superior a 70% de acontecer.';
                                    $fact->save();
                                }
                            }                                                    
                        }

                        break;
                    }
                    case 'HT': {
                        $hTFacts = JFHFact::query()
                            ->where('fixture_id', $fixture->id)
                            ->where('fact', 'HT')
                            ->where('is_live', 1)
                            ->get();

                        if ($this->isHT($fixtureData->status->short) && $hTFacts->isEmpty()) {
                            // verificar o resultado
                            // o jogo está empatado a zero?
                            // há equipas favoritas envolvidas?
                            // o numero de golos marcados até ao momento está acima da média?
                            // ambas as equipas já marcaram?
                            // como estao as estatisticas em relação às odds iniciais?
                        }
                        
                        break;
                    }
                    case 'ZERO_ZERO_RESULT': {
                        if ($fixtureLive) {
                            if ($this->isInTime($elapsed, 15)) { // verificação a cada 15 minutos
                                if ($homeGoals == 0 && $awayGoals == 0) { // o resultado está empatado a zero
                                    $half = $this->isFirstHalf($elapsed) ? '1ª parte' : '2ª parte';

                                    foreach ([$fixture->HomeTeam, $fixture->AwayTeam] as $team) {
                                        $games = $this->getFixturesForATeamInCurrentSeason($fixture, $team->id);

                                        $n = 0;
                                        foreach ($games as $game) {
                                            $hasGoalInTimeframe = $game->Events
                                                ->where('elapsed', '<=', $elapsed)
                                                ->where('type', 'Goal')
                                                ->where('detail', '!=', 'Missed Penalty')
                                                ->isNotEmpty();

                                            if ($hasGoalInTimeframe) {
                                                $n++;
                                            }
                                        }
                                        $total = $games->count();
                                        $percentage = round(($n / $total) * 100);

                                        $fact = JFHFact::firstOrNew(['fixture_id' => $fixture->id, 'team_id' => $team->id, 'fact' => 'ZERO_ZERO_RESULT', 'is_live' => 1, 'elapsed' => $elapsed]);
                                        $fact->description = 'Estamos na ' . $half . ' do jogo, onde decorridos ' . $elapsed . ' minutos, o resultado está 0-0. Esta época, a equipa ' . $team->name . ' marcou golos ' . ($this->isFirstHalf($elapsed) ? ('na 1ª parte') : 'até aos ' . $elapsed . 'minutos') . ' em ' . ($n . '/' . $total) . ' dos seus jogos. O que dá uma média de ' . $percentage . '% dos jogos.';
                                        $fact->save();
                                    }
                                }
                            }
                        }

                        break;
                    }
                    case '15_MINUTES_TO_HT':                        
                    case '30_MINUTES_TO_FT':
                        // Faltam 30 minutos para o fim do jogo. Verificar se as equipas estão empatadas.
                        // Calcular nos jogos anteriores, a percentagem de vezes que as equipas estão empatadas a este instante.
                    case '15_MINUTES_TO_FT': {
                        if ($fixtureLive) {
                            if ($this->isFirstHalf($elapsed)) {
                                $fiteenMinutesToHTFacts = JFHFact::query()
                                    ->where('fixture_id', $fixture->id)
                                    ->where('fact', '15_MINUTES_TO_HT')
                                    ->where('is_live', 1)
                                    ->get();

                                if ($elapsed >= 30 && $fiteenMinutesToHTFacts->isEmpty()) {
                                    // Faltam 15 minutos para o intervalo. Verificar se o jogo está empatado ou não.
                                    // Calcular nos jogos anteriores da época actual, a percentagem de resultados de ambas as equipas ao intervalo

                                    foreach ([$fixture->HomeTeam, $fixture->AwayTeam] as $team) {
                                        $games = $this->getFixturesForATeamInCurrentSeason($fixture, $team->id);
                                        $playingHome = $team->id == $fixture->home_team_id;

                                        $data = [
                                            'TOTAL' => [
                                                'wins' => 0,
                                                'draws' => 0,
                                                'loses' => 0,
                                                'total' => 0,
                                            ]
                                        ];

                                        $data[$playingHome ? 'HOME' : 'AWAY'] = [
                                            'wins' => 0,
                                            'draws' => 0,
                                            'loses' => 0,
                                            'total' => 0,
                                        ];
                                        

                                        foreach ($games as $game) {
                                            $data['TOTAL']['total']++;

                                            if ($game->home_team_id == $team->id) { // teamId a jogar em casa
                                                $data['TOTAL']['wins'] += $game->ht_home_goals > $game->ht_away_goals ? 1 : 0;
                                                $data['TOTAL']['draws'] += $game->ht_home_goals == $game->ht_away_goals ? 1 : 0;
                                                $data['TOTAL']['loses'] += $game->ht_home_goals < $game->ht_away_goals ? 1 : 0;

                                                $data['HOME']['total']++;
                                                $data['HOME']['wins'] += $game->ht_home_goals > $game->ht_away_goals ? 1 : 0;
                                                $data['HOME']['draws'] += $game->ht_home_goals == $game->ht_away_goals ? 1 : 0;
                                                $data['HOME']['loses'] += $game->ht_home_goals < $game->ht_away_goals ? 1 : 0;
                                            }
                                            else { // teamId a jogar fora
                                                $data['TOTAL']['wins'] += $game->ht_away_goals > $game->ht_home_goals ? 1 : 0;
                                                $data['TOTAL']['draws'] += $game->ht_away_goals == $game->ht_home_goals ? 1 : 0;
                                                $data['TOTAL']['loses'] += $game->ht_away_goals < $game->ht_home_goals ? 1 : 0;

                                                $data['AWAY']['total']++;
                                                $data['AWAY']['wins'] += $game->ht_away_goals > $game->ht_home_goals ? 1 : 0;
                                                $data['AWAY']['draws'] += $game->ht_away_goals == $game->ht_home_goals ? 1 : 0;
                                                $data['AWAY']['loses'] += $game->ht_away_goals < $game->ht_home_goals ? 1 : 0;
                                            }
                                        }

                                        $totalWinsPercentage = round(($data['TOTAL']['wins'] / $data['TOTAL']['total']) * 100);
                                        $totalDrawsPercentage = round(($data['TOTAL']['draws'] / $data['TOTAL']['total']) * 100);
                                        $totalLosesPercentage = round(($data['TOTAL']['loses'] / $data['TOTAL']['total']) * 100);

                                        $homeWinsPercentage = round(($data['HOME']['draws'] / $data['HOME']['total']) * 100);
                                        $homeDrawsPercentage = round(($data['HOME']['draws'] / $data['HOME']['total']) * 100);
                                        $homeLosesPercentage = round(($data['HOME']['loses'] / $data['HOME']['total']) * 100);

                                        $awayWinsPercentage = round(($data['AWAY']['wins'] / $data['AWAY']['total']) * 100);
                                        $awayDrawsPercentage = round(($data['AWAY']['draws'] / $data['AWAY']['total']) * 100);
                                        $awayLosesPercentage = round(($data['AWAY']['loses'] / $data['AWAY']['total']) * 100);

                                        $fact = JFHFact::firstOrNew(['fixture_id' => $fixture->id, 'team_id' => $team->id, 'fact' => '15_MINUTES_TO_HT', 'is_live' => 1, 'elapsed' => $elapsed]);
                                        $fact->description = 'Faltam 15 minutos para o intervalo e o resultado está ' . $score . '. Nesta época, esta equipa ao intervalo vencia ' . $totalWinsPercentage . '% das vezes, empatava ' . $totalDrawsPercentage . '% das vezes e perdia ' . $totalLosesPercentage . '% das vezes. Se considerarmos apenas os jogos ' . ($playingHome ? 'em casa' : 'fora') . ', a equipa vencia ' . ($playingHome ? $homeWinsPercentage : $awayWinsPercentage) . '% das vezes, empatava ' . ($playingHome ? $homeDrawsPercentage : $awayDrawsPercentage) . '% das vezes e perdia ' . ($playingHome ? $homeLosesPercentage : $awayLosesPercentage) . '% das vezes.';
                                        $fact->save();
                                    }
                                }
                            }
                            else {
                                $minutesToFTFacts = JFHFact::query()
                                    ->where('fixture_id', $fixture->id)
                                    ->where('fact', ['15_MINUTES_TO_FT', '30_MINUTES_TO_FT'])
                                    ->where('is_live', 1)
                                    ->get();

                                $noExistsFirstFact = $minutesToFTFacts->where('fact', '15_MINUTES_TO_FT')->isEmpty();
                                $noExistsSecondFact = $minutesToFTFacts->where('fact', '30_MINUTES_TO_FT')->isEmpty();

                                if ((($elapsed >= 60 && $elapsed < 74) && $noExistsFirstFact) || ($elapsed >= 75 && $noExistsSecondFact)) {
                                    foreach ([$fixture->HomeTeam, $fixture->AwayTeam] as $team) {
                                        $games = $this->getFixturesForATeamInCurrentSeason($fixture, $team->id);
                                        $playingHome = $team->id == $fixture->home_team_id;

                                        $data = [
                                            'TOTAL' => [
                                                'wins' => 0,
                                                'draws' => 0,
                                                'loses' => 0,
                                                'total' => 0,
                                            ]
                                        ];

                                        $data[$playingHome ? 'HOME' : 'AWAY'] = [
                                            'wins' => 0,
                                            'draws' => 0,
                                            'loses' => 0,
                                            'total' => 0,
                                        ];
                                        

                                        foreach ($games as $game) {
                                            $data['TOTAL']['total']++;

                                            if ($game->home_team_id == $team->id) { // teamId a jogar em casa
                                                $data['TOTAL']['wins'] += $game->ft_home_goals > $game->ft_away_goals ? 1 : 0;
                                                $data['TOTAL']['draws'] += $game->ft_home_goals == $game->ft_away_goals ? 1 : 0;
                                                $data['TOTAL']['loses'] += $game->ft_home_goals < $game->ft_away_goals ? 1 : 0;

                                                $data['HOME']['total']++;
                                                $data['HOME']['wins'] += $game->ft_home_goals > $game->ft_away_goals ? 1 : 0;
                                                $data['HOME']['draws'] += $game->ft_home_goals == $game->ft_away_goals ? 1 : 0;
                                                $data['HOME']['loses'] += $game->ft_home_goals < $game->ft_away_goals ? 1 : 0;
                                            }
                                            else { // teamId a jogar fora
                                                $data['TOTAL']['wins'] += $game->ft_away_goals > $game->ft_home_goals ? 1 : 0;
                                                $data['TOTAL']['draws'] += $game->ft_away_goals == $game->ft_home_goals ? 1 : 0;
                                                $data['TOTAL']['loses'] += $game->ft_away_goals < $game->ft_home_goals ? 1 : 0;

                                                $data['AWAY']['total']++;
                                                $data['AWAY']['wins'] += $game->ft_away_goals > $game->ft_home_goals ? 1 : 0;
                                                $data['AWAY']['draws'] += $game->ft_away_goals == $game->ft_home_goals ? 1 : 0;
                                                $data['AWAY']['loses'] += $game->ft_away_goals < $game->ft_home_goals ? 1 : 0;
                                            }                                            
                                        }

                                        $totalWinsPercentage = round(($data['TOTAL']['wins'] / $data['TOTAL']['total']) * 100);
                                        $totalDrawsPercentage = round(($data['TOTAL']['draws'] / $data['TOTAL']['total']) * 100);
                                        $totalLosesPercentage = round(($data['TOTAL']['loses'] / $data['TOTAL']['total']) * 100);

                                        $homeWinsPercentage = round(($data['HOME']['wins'] / $data['HOME']['total']) * 100);
                                        $homeDrawsPercentage = round(($data['HOME']['draws'] / $data['HOME']['total']) * 100);
                                        $homeLosesPercentage = round(($data['HOME']['loses'] / $data['HOME']['total']) * 100);

                                        $awayWinsPercentage = round(($data['AWAY']['wins'] / $data['AWAY']['total']) * 100);
                                        $awayDrawsPercentage = round(($data['AWAY']['draws'] / $data['AWAY']['total']) * 100);
                                        $awayLosesPercentage = round(($data['AWAY']['loses'] / $data['AWAY']['total']) * 100);

                                        $fact = JFHFact::firstOrNew(['fixture_id' => $fixture->id, 'team_id' => $team->id, 'fact' => '15_MINUTES_TO_FT', 'is_live' => 1, 'elapsed' => $elapsed]);
                                        $fact->description = sprintf(
                                            'Faltam %d minutos para o fim do jogo e o resultado está %s. Nesta época, esta equipa venceu %d%% das vezes, empatou %d%% das vezes e perdeu %d%% das vezes. Se considerarmos apenas os jogos %s, a equipa venceu %d%% das vezes, empatou %d%% das vezes e perdeu %d%% das vezes.',
                                            90 - $elapsed,
                                            $score,
                                            $totalWinsPercentage,
                                            $totalDrawsPercentage,
                                            $totalLosesPercentage,
                                            $playingHome ? 'em casa' : 'fora',
                                            $playingHome ? $homeWinsPercentage : $awayWinsPercentage,
                                            $playingHome ? $homeDrawsPercentage : $awayDrawsPercentage,
                                            $playingHome ? $homeLosesPercentage : $awayLosesPercentage
                                        );
                                        $fact->save();
                                    }
                                }
                            }
                        }

                        break;
                    }
                    default:
                        break;
                }
            }
        }
    }

    private function getFixtureFromResponse($fixtures, $api_id)
    {
        return collect($fixtures)->first(function ($item) use ($api_id) {
            return $item->fixture->id == $api_id;
        });
    }

    private function isFixtureLive($status)
    {
        return in_array($status, ['LIVE', '1H', 'HT', '2H', 'ET', 'BT', 'P', 'SUSP', 'INT']);
    }

    private function isInTime($elapsed, $minutes)
    {
        return ($elapsed % $minutes) == 0;
    }

    private function isFirstHalf($elapsed)
    {
        return $elapsed <= 45;
    }

    private function isHT($status)
    {
        return $status == 'HT';
    }

    private function getOdds($response)
    {
        return $response && sizeof($response->response) ? $response->response[0] : null;
    }

    private function handleOdds($fixture)
    {
        $fixtureOdds = Utils::curl("https://api-football-v1.p.rapidapi.com/v3/odds/live?fixture=" . $fixture->api_id, 'AddFixturesLiveOddsJFH');        
        $oddsData = $this->getOdds($fixtureOdds);        

        if (!$oddsData) {
            return;
        }
        
        JFHFixture::query()
            ->where('id', $fixture->id)
            ->update(['seconds' => $oddsData->fixture->status->seconds]);

        JFHLiveOddOddValue::query()
            ->where('fixture_id', $fixture->id)
            ->delete();

        JFHLiveOddOdd::query()
            ->where('fixture_id', $fixture->id)
            ->delete();

        JFHLiveOdd::query()
            ->where('fixture_id', $fixture->id)
            ->delete();

        $liveOdd = JFHLiveOdd::firstOrNew(['fixture_id' => $fixture->id]);
        $liveOdd->status = json_encode($oddsData->status);
        $liveOdd->save();        

        foreach ($oddsData->odds as $odd) {
            $liveOddOdd = JFHLiveOddOdd::firstOrNew([
                'fixture_id' => $fixture->id,
                'live_odds_id' => $liveOdd->id,
                '_id' => $odd->id
            ]);
            $liveOddOdd->name = $odd->name;
            $liveOddOdd->save();            

            foreach ($odd->values as $value) {
                $liveOddOddValue = JFHLiveOddOddValue::firstOrNew([
                    'live_odds_odd_id' => $liveOddOdd->id,
                    'fixture_id' => $fixture->id,
                    'value' => $value->value
                ]);
                $liveOddOddValue->odd = $value->odd;
                $liveOddOddValue->handicap = $value->handicap;
                $liveOddOddValue->main = $value->main;
                $liveOddOddValue->suspended = $value->suspended;
                $liveOddOddValue->save();
            }
        }
    }

    private function getStandings($fixture) {
        $standings = JFHStanding::query()
            ->where('season_id', $fixture->season_id)
            ->where('league_id', $fixture->league_id)
            ->orderBy('rank')
            ->get();

        $data = [];

        foreach ($standings as $row) {
            if (in_array($row->team_id, [$fixture->home_team_id, $fixture->away_team_id])) {
                $data[$row->team_id]= $row->rank;
            }
        }

        $fixture->standings = $data;
        $fixture->save();

        return $data;
    }

    private function getRedCards() {
        $cards = [];

        $possibilities = [
            'homeTeamWinning' => ['1-0', '2-0', '2-1'],
            'awayTeamWinning' => ['0-1', '0-2', '1-2'],
        ];

        foreach ($possibilities as $values) {
            foreach ($values as $card) {
                $cards []= $card;
            }
        }

        return $cards;
    }

    private function getScores() {
        $scores = [];

        $possibilities = [
            'draws' => ['1-1', '2-2', '3-3'],
            'homeTeamWinning' => ['1-0', '2-0', '3-0'],
            'awayTeamWinning' => ['0-1', '0-2', '0-3'],
            'homeTeamLeading' => ['2-1', '3-1', '3-2'],
            'awayTeamLeading' => ['1-2', '1-3', '2-3'],
        ];

        foreach ($possibilities as $values) {
            foreach ($values as $score) {
                $scores []= $score;
            }
        }

        return $scores;
    }

    /**
     * @param $score string - score do jogo
     * @param $scoresData array - array com os ids das equipas que marcaram golos
     * @param $fixture JFHFixture - jogo
     * @param $fixtureData stdClass - dados do jogo
     * @param $teamId int - id da equipa que marcou o último golo
     * @param $favoriteTeam - id da equipa favorita para o jogo ou null
     * @param $lastThreeSeasons Collection - últimas 3 temporadas
     */
    private function handleScores($score, $scoresData, $fixture, $elapsed, $teamId, $favoriteTeam, $lastThreeSeasons) {
        $lastThreeSeasonsIds = $lastThreeSeasons
            ->map(function($item) { return $item->id; })
            ->toArray();

        $playingHome = $fixture->home_team_id == $teamId;

        $games = JFHFixture::query()
            ->with(['Events'])
            ->whereHas('Events', function($q) {
                $q->where('type', 'Goal')
                    ->where('detail', '!=', 'Missed Penalty');
            })
            ->whereIn('season_id', $lastThreeSeasonsIds)
            ->where(function($q) use ($teamId) {
                $q->where('home_team_id', $teamId)
                    ->orWhere('away_team_id', $teamId);
            })
            ->whereRaw('starts_at < ?', $fixture->starts_at)
            ->where('status', ['FT', 'AET', 'PEN', 'PST', 'CANC', 'ABD', 'AWD', 'WO'])
            ->orderBy('starts_at', 'asc')
            ->get()
            ->filter(function ($item) use ($scoresData, $teamId) {
                $gameGoalsEvents = $item->Events
                    ->where('type', 'Goal')
                    ->where('detail', '!=', 'Missed Penalty')
                    ->orderBy('elapsed', 'asc')
                    ->limit(sizeof($scoresData))
                    ->get();

                $gameScoresData = [];

                foreach($gameGoalsEvents as $event) {
                    $gameScoresData []= $event->team_id;
                }

                $scoresData = array_map(function ($item) use ($teamId) {
                    return $item == $teamId ? $item : null;
                }, $scoresData);

                return $gameScoresData === $scoresData; // verifica se os arrays são iguais
            });

        if ($games->count() == 0) {
            $fact = JFHFact::firstOrNew(['fixture_id' => $fixture->id, 'team_id' => $teamId, 'fact' => 'GOAL', 'is_live' => 1, 'elapsed' => $elapsed]);
            $fact->description = 'Golo do ' . $this->getTeamNameById($fixture, $teamId) . '! ' . ($favoriteTeam === $teamId ? 'Esta equipa era favorita a vencer este jogo!' : '') . ' Ainda não há dados suficientes para fazer análises.';
            $fact->save();
            return;
        }

        $data = [];

        if ($this->isFirstHalf($elapsed)) {
            $data = [
                'HT' => [
                    'wins' => 0,
                    'draws' => 0,
                    'loses' => 0,
                    'total' => 0,
                ],
                'HOME' => [ // Full time
                    'wins' => 0,
                    'draws' => 0,
                    'loses' => 0,
                    'total' => 0,
                ],
                'AWAY' => [ // Full time
                    'wins' => 0,
                    'draws' => 0,
                    'loses' => 0,
                    'total' => 0,
                ],
            ];

            foreach ($games as $game) {
                $data['HT']['total']++;
                $data['HOME']['total'] += $game->home_team_id == $teamId ? 1 : 0;
                $data['AWAY']['total'] += $game->away_team_id == $teamId ? 1 : 0;

                if ($game->home_team_id == $teamId) { // teamId a jogar em casa
                    $data['HT']['wins'] += $game->ht_home_goals > $game->ht_away_goals ? 1 : 0;
                    $data['HT']['draws'] += $game->ht_home_goals == $game->ht_away_goals ? 1 : 0;
                    $data['HT']['loses'] += $game->ht_home_goals < $game->ht_away_goals ? 1 : 0;

                    $data['HOME']['wins'] += $game->ft_home_goals > $game->ft_away_goals ? 1 : 0;
                    $data['HOME']['draws'] += $game->ft_home_goals == $game->ft_away_goals ? 1 : 0;
                    $data['HOME']['loses'] += $game->ft_home_goals < $game->ft_away_goals ? 1 : 0;
                }
                else { // teamId a jogar fora
                    $data['HT']['wins'] += $game->ht_away_goals > $game->ht_home_goals ? 1 : 0;
                    $data['HT']['draws'] += $game->ht_away_goals == $game->ht_home_goals ? 1 : 0;
                    $data['HT']['loses'] += $game->ht_away_goals < $game->ht_home_goals ? 1 : 0;

                    $data['AWAY']['wins'] += $game->ft_away_goals > $game->ft_home_goals ? 1 : 0;
                    $data['AWAY']['draws'] += $game->ft_away_goals == $game->ft_home_goals ? 1 : 0;
                    $data['AWAY']['loses'] += $game->ft_away_goals < $game->ft_home_goals ? 1 : 0;
                }
            }
        }
        else {
            $data = [
                'FT' => [
                    'wins' => 0,
                    'draws' => 0,
                    'loses' => 0,
                    'total' => 0,
                ],
                'HOME' => [ // Full time
                    'wins' => 0,
                    'draws' => 0,
                    'loses' => 0,
                    'total' => 0,
                ],
                'AWAY' => [ // Full time
                    'wins' => 0,
                    'draws' => 0,
                    'loses' => 0,
                    'total' => 0,
                ],
            ];

            foreach ($games as $game) {
                $data['FT']['total']++;
                $data['HOME']['total'] += $game->home_team_id == $teamId ? 1 : 0;
                $data['AWAY']['total'] += $game->away_team_id == $teamId ? 1 : 0;

                if ($game->home_team_id == $teamId) { // teamId a jogar em casa
                    $data['FT']['wins'] += $game->ft_home_goals > $game->ft_away_goals ? 1 : 0;
                    $data['FT']['draws'] += $game->ft_home_goals == $game->ft_away_goals ? 1 : 0;
                    $data['FT']['loses'] += $game->ft_home_goals < $game->ft_away_goals ? 1 : 0;

                    $data['HOME']['wins'] += $game->ft_home_goals > $game->ft_away_goals ? 1 : 0;
                    $data['HOME']['draws'] += $game->ft_home_goals == $game->ft_away_goals ? 1 : 0;
                    $data['HOME']['loses'] += $game->ft_home_goals < $game->ft_away_goals ? 1 : 0;
                }
                else { // teamId a jogar fora
                    $data['FT']['wins'] += $game->ft_away_goals > $game->ft_home_goals ? 1 : 0;
                    $data['FT']['draws'] += $game->ft_away_goals == $game->ft_home_goals ? 1 : 0;
                    $data['FT']['loses'] += $game->ft_away_goals < $game->ft_home_goals ? 1 : 0;

                    $data['AWAY']['wins'] += $game->ft_away_goals > $game->ft_home_goals ? 1 : 0;
                    $data['AWAY']['draws'] += $game->ft_away_goals == $game->ft_home_goals ? 1 : 0;
                    $data['AWAY']['loses'] += $game->ft_away_goals < $game->ft_home_goals ? 1 : 0;
                }
            }            
        }

        $fact = JFHFact::firstOrNew(['fixture_id' => $fixture->id, 'team_id' => $teamId, 'fact' => 'GOAL', 'is_live' => 1, 'elapsed' => $elapsed]);
        if ($this->isFirstHalf($elapsed)) { // até ao intervalo
            if ($data['HT']['total'] > 0) {
                $fact->description = 'Golo do ' . $this->getTeamNameById($fixture, $teamId) . '! ' . ($favoriteTeam === $teamId ? 'Esta equipa era favorita a vencer este jogo!' : '') . ' Nas últimas três temporadas, quando o resultado estava ' . $score . ', esta equipa ganhou ' . (($data['HT']['wins'] / $data['HT']['total']) * 100) . '% das vezes ao intervalo, empatou ' . (($data['HT']['draws'] / $data['HT']['total']) * 100) . '% das vezes e perdeu ' . ($data['HT']['loses'] / $data['HT']['total'] * 100) . '% das vezes ao intervalo. Considerando apenas os jogos ' . ($playingHome ? 'em casa' : 'fora') .  ', esta equipa ao intervalo ganhou ' . (($data[$playingHome ? 'HOME' : 'AWAY']['wins'] / $data[$playingHome ? 'HOME' : 'AWAY']['total']) * 100) . '% das vezes, empatou ' . (($data[$playingHome ? 'HOME' : 'AWAY']['draws'] / $data[$playingHome ? 'HOME' : 'AWAY']['total']) * 100) . ' das vezes e perdeu ' . (($data[$playingHome ? 'HOME' : 'AWAY']['loses'] / $data[$playingHome ? 'HOME' : 'AWAY']['total']) * 100) . '% das vezes.';
            }
        }
        else { // até ao fim do jogo
            if ($data['FT']['total'] > 0) {
                $fact->description = 'Golo do ' . $this->getTeamNameById($fixture, $teamId) . '! ' . ($favoriteTeam === $teamId ? 'Esta equipa era favorita a vencer este jogo!' : '') . ' Nas últimas três temporadas, quando o resultado estava ' . $score . ', esta equipa ganhou ' . (($data['FT']['wins'] / $data['FT']['total']) * 100) . '% das vezes no fim do jogo, empatou ' . (($data['FT']['draws'] / $data['FT']['total']) * 100) . '% das vezes e perdeu ' . ($data['FT']['loses'] / $data['FT']['total'] * 100) . '% das vezes ao intervalo. Considerando apenas os jogos ' . ($playingHome ? 'em casa' : 'fora') .  ', esta equipa no fim do jogo ganhou ' . (($data[$playingHome ? 'HOME' : 'AWAY']['wins'] / $data[$playingHome ? 'HOME' : 'AWAY']['total']) * 100) . '% das vezes, empatou ' . (($data[$playingHome ? 'HOME' : 'AWAY']['draws'] / $data[$playingHome ? 'HOME' : 'AWAY']['total']) * 100) . ' das vezes e perdeu ' . (($data[$playingHome ? 'HOME' : 'AWAY']['loses'] / $data[$playingHome ? 'HOME' : 'AWAY']['total']) * 100) . '% das vezes.';
            }
        }
        $fact->save();
    }

    
    /**
     * @param $score string - score dos cartões
     * @param $cardsData array - array com os ids das equipas que levaram cartões vermelhos
     * @param $fixture JFHFixture - jogo
     * @param $teamId int - id da equipa que levou o último cartão vermelho
     * @param $elapsed int - minuto do jogo
     */
    private function handleRedCards($score, $cardsData, $fixture, $teamId, $elapsed) {
        $playingHome = $fixture->home_team_id == $teamId;

        $games = JFHFixture::query()
            ->with(['Events'])
            ->whereHas('Events', function($q) {
                $q->where('type', 'Card')
                    ->where('detail', 'Red Card');
            })
            ->where(function ($q) use ($teamId) {
                $q->where('home_team_id', $teamId)
                    ->orWhere('away_team_id', $teamId);
            })
            ->where('season_id', $fixture->season_id) // apenas esta época
            ->whereRaw('starts_at < ?', $fixture->starts_at)
            ->where('status', ['FT', 'AET', 'PEN', 'PST', 'CANC', 'ABD', 'AWD', 'WO'])
            ->get()
            ->filter(function ($item) use ($cardsData, $teamId) {
                $gameCardsEvents = $item->Events
                    ->where('type', 'Card')
                    ->where('detail', 'Red Card')
                    ->orderBy('elapsed', 'asc')
                    ->limit(sizeof($cardsData))
                    ->get();

                $gameCardsData = [];

                foreach($gameCardsEvents as $event) {
                    $gameCardsData []= $event->team_id;
                }

                $gameCardsData = array_map(function ($item) use ($teamId) {
                    return $item == $teamId ? $item : null;
                }, $gameCardsData);

                return $gameCardsData === $cardsData; // verifica se os arrays são iguais
            });

        if ($games->count() == 0) {
            $fact = JFHFact::firstOrNew(['fixture_id' => $fixture->id, 'team_id' => $teamId, 'fact' => 'RED_CARD', 'is_live' => 1, 'elapsed' => $elapsed]);
            $fact->description = 'É a primeira vez que esta equipa levou um cartão vermelho nesta época. ' . ($fixture->home_team_id == $teamId ? 'Embora a jogar em casa' : 'A jogar fora') . ', está em desvantagem numérica aos ' . $elapsed . ' minutos de jogo.';
            $fact->save();
            return;
        }

        $data = [
            'FT' => [
                'wins' => 0,
                'draws' => 0,
                'loses' => 0,
                'total' => 0,
            ],
            'HOME' => [ // Full time
                'wins' => 0,
                'draws' => 0,
                'loses' => 0,
                'total' => 0,
            ],
            'AWAY' => [ // Full time
                'wins' => 0,
                'draws' => 0,
                'loses' => 0,
                'total' => 0,
            ],
        ];

        foreach ($games as $game) {
            $data['FT']['total']++;
            $data['HOME']['total'] += $game->home_team_id == $teamId ? 1 : 0;
            $data['AWAY']['total'] += $game->away_team_id == $teamId ? 1 : 0;

            if ($game->home_team_id == $teamId) { // teamId a jogar em casa
                $data['FT']['wins'] += $game->ft_home_goals > $game->ft_away_goals ? 1 : 0;
                $data['FT']['draws'] += $game->ft_home_goals == $game->ft_away_goals ? 1 : 0;
                $data['FT']['loses'] += $game->ft_home_goals < $game->ft_away_goals ? 1 : 0;

                $data['HOME']['wins'] += $game->ft_home_goals > $game->ft_away_goals ? 1 : 0;
                $data['HOME']['draws'] += $game->ft_home_goals == $game->ft_away_goals ? 1 : 0;
                $data['HOME']['loses'] += $game->ft_home_goals < $game->ft_away_goals ? 1 : 0;
            }
            else { // teamId a jogar fora
                $data['FT']['wins'] += $game->ft_away_goals > $game->ft_home_goals ? 1 : 0;
                $data['FT']['draws'] += $game->ft_away_goals == $game->ft_home_goals ? 1 : 0;
                $data['FT']['loses'] += $game->ft_away_goals < $game->ft_home_goals ? 1 : 0;

                $data['AWAY']['wins'] += $game->ft_away_goals > $game->ft_home_goals ? 1 : 0;
                $data['AWAY']['draws'] += $game->ft_away_goals == $game->ft_home_goals ? 1 : 0;
                $data['AWAY']['loses'] += $game->ft_away_goals < $game->ft_home_goals ? 1 : 0;
            }
        }

        $fact = JFHFact::firstOrNew(['fixture_id' => $fixture->id, 'team_id' => $teamId, 'fact' => 'RED_CARD', 'is_live' => 1, 'elapsed' => $elapsed]);
        $fact->description = 'A equipa ' . $this->getTeamNameById($fixture, $teamId) . ' levou um cartão vermelho. ' . ($fixture->home_team_id == $teamId ? 'Embora a jogar em casa' : 'A jogar fora') . ', está em desvantagem numérica com menos ' . $numOfCards . ' jogador(es) aos ' . $elapsed . ' minutos de jogo. Esta época, em inferioridade numérica com menos ' . $numOfCards . ' jogador(es), nos jogos que realizou, esta equipa venceu ' . (($data['FT']['wins'] / $data['FT']['total']) * 100) . '% das vezes, empatou ' . (($data['FT']['draws'] / $data['FT']['total']) * 100) . '% e perdeu ' . (($data['FT']['loses'] / $data['FT']['total']) * 100) . '% dos seus jogos. Se considerarmos apenas os ' . ($fixture->home_team_id == $teamId ? 'jogos em casa' : 'jogos fora') . ', esta equipa venceu ' . (($data[$playingHome ? 'HOME' : 'AWAY']['wins'] / $data[$playingHome ? 'HOME' : 'AWAY']['total']) * 100) . '% das vezes, empatou ' . (($data[$playingHome ? 'HOME' : 'AWAY']['draws'] / $data[$playingHome ? 'HOME' : 'AWAY']['total']) * 100) . '% e perdeu ' . (($data[ $playingHome ? 'HOME' : 'AWAY']['loses'] / $data[ $playingHome ? 'HOME' : 'AWAY']['total']) * 100) . '%. dos seus jogos em inferioridade numérica.';
        $fact->save();
    }

    private function getTeamNameById($fixture, $teamId) {
        return $fixture->home_team_id == $teamId ? $fixture->HomeTeam->name : $fixture->AwayTeam->name;
    }

    private function getFavoriteTeam($fixture) {
        $matchWinnerBet = collect([]);

        $matchWinnerBetData = $fixture->Odds
            ->flatMap->Bets
            ->firstWhere('Bet.name', 'Match Winner');

        if ($matchWinnerBetData) {
            $matchWinnerBet = $matchWinnerBetData->Values->map(function ($value) {
                return ['value' => $value->value, 'odd' => $value->odd];
            });
        }

        // check if there is any results that suggest a favorite team to win
        $favoriteResultOdd = $matchWinnerBet->sortBy('odd')->filter(function ($item) {
            return ((1 / $item['odd']) * 100) > 75;
        })->first();

        return $favoriteResultOdd ? ($favoriteResultOdd['value'] == 'Home' ? $fixture->home_team_id : ($favoriteResultOdd['value'] == 'Away' ? $fixture->away_team_id : null)): null;
    }

    private function inactiveOldRecords($fixture, $type, $teamId = null) {
        JFHFact::query()
            ->where('fixture_id', $fixture->id)
            ->where(function($q) use ($type) {
                if (is_array($type)) {
                    $q->whereIn('fact', $type);
                }
                else {
                    $q->where('fact', $type);
                }
            })
            ->where('is_live', 1)
            ->when($teamId, function ($q) use ($teamId) {
                return $q->where('team_id', $teamId);
            })
            ->update(['still_valid' => 0]);
    }
    
    private function getStats($fixture) {
        $json = Utils::curl("https://api-football-v1.p.rapidapi.com/v3/fixtures?id=" . $fixture->api_id, 'GetLiveFixtures');
        
        if (!isset($json->response)) {
            return;
        }

        // events
        // lineups
        // statistics
        // players
        //   team
        //     players
        //        player
        //        statistics

        return $json->response[0];
    }

    private function getFixtureDataLiveEvents($fixtureData, $type, $detail = null, $operator = '!=') {
        return collect($fixtureData->events)->filter(function ($item) use ($type, $detail, $operator) {
            if ($detail) {
                return strtolower($item->type) == strtolower($type) && (
                    $operator == '!=' ?
                    strtolower($item->detail) != strtolower($detail)
                    :
                    strtolower($item->detail) == strtolower($detail)
                );
            }

            return strtolower($item->type) == strtolower($type);
        });
    }

    private function getFixturesForATeamInCurrentSeason($fixture, $teamId) {
        return HandleCache::setOrGetStaticQuery(
            JFHFixture::query()
                ->with(['Stats', 'Events'])        
                ->where(function ($q) use ($teamId) {
                    $q->where('home_team_id', $teamId)
                        ->orWhere('away_team_id', $teamId);
                })
                ->where('season_id', $fixture->season_id)
                ->whereRaw('starts_at < ?', $fixture->starts_at)
                ->where('status', ['FT', 'AET', 'PEN', 'PST', 'CANC', 'ABD', 'AWD', 'WO'])
        );
    }
}