<?php

namespace App\Console\Commands\JogosFutebolHoje;

use App\Helpers\Utils;
use App\Models\JFHFixture;
use App\Models\JFHFixtureStat;
use App\Models\JFHTeam;
use Illuminate\Console\Command;

class AddFixturesStats extends Command
{
    protected $signature = 'jfh:get_stats'; // every minute

    protected $description = 'Get stats from fixtures in play';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $fixtures = JFHFixture::query()
            ->whereRaw('NOW() BETWEEN DATE_SUB(starts_at, INTERVAL 10 MINUTE) AND DATE_ADD(starts_at, INTERVAL 150 MINUTE)')
            ->where('status', '<>', 'FT')
            ->get();

        foreach($fixtures as $fixture) {            
            $json = Utils::curl("https://api-football-v1.p.rapidapi.com/v3/fixtures/statistics?fixture=" . $fixture->api_id, 'AddFixturesStats');

            if (isset($json->response)) {
                foreach($json->response as $item) {
                    $team = JFHTeam::query()
                        ->where('api_id', $item->team->id)
                        ->first();

                    if ($team) {
                        foreach($item->statistics as $statistic) {
                            $stat = JFHFixtureStat::firstOrNew(['team_id' => $team->id, 'type' => $statistic->type, 'fixture_id' => $fixture->id]);
                            $oldValue = $stat->value;
                            $stat->value = $statistic->value;

                            if ($statistic->type == 'Ball Possession') {
                                $details = $stat->details ?? [];
                                $details[$fixture->elapsed] = str_replace('%', '', $statistic->value);
                                
                                $stat->details = $details;
                            }

                            if (in_array($statistic->type, ['Total Shots', 'Corner Kicks', 'Yellow Cards', 'Ball Possession'])) {
                                if (!in_array($fixture->elapsed, [10, 20, 30, 40, 50, 60, 70, 80, 90])) {
                                    $stat->extra = $stat->exists ? ($statistic->value - $oldValue) : $statistic->value;
                                }
                            }

                            $stat->save();
                        }
                    }
                }
            }

            usleep(200);
        }
    }
}