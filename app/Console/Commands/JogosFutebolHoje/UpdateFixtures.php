<?php

namespace App\Console\Commands\JogosFutebolHoje;

use App\Helpers\Utils;
use App\Models\JFHFixture;
use App\Models\JFHTeam;
use App\Models\JFHVenue;
use Illuminate\Console\Command;

class UpdateFixtures extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'jfh:update_fixtures';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update fixtures from the API Football';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $json = Utils::curl("https://api-football-v1.p.rapidapi.com/v3/fixtures?live=all", 'AddFixturesJFH');

        if (isset($json->response)) {
            $fixturesLiveIds = array_map(function ($item) {
                return $item->fixture->id;
            }, $json->response);

            $fixturesLive = JFHFixture::query()
                ->whereIn('api_id', $fixturesLiveIds)
                ->get();            

            foreach ($json->response as $item) {
                $fixture = $fixturesLive->firstWhere('api_id', $item->fixture->id);

                if ($fixture) {
                    $home_team = JFHTeam::query()->where(['api_id' => $item->teams->home->id])->first();
                    $away_team = JFHTeam::query()->where(['api_id' => $item->teams->away->id])->first();

                    if ($home_team && $away_team) {
                        $fixture->status = $item->fixture->status->short;
                        $fixture->elapsed = $item->fixture->status->elapsed ?? 0;
                        $fixture->home_goals = $item->goals->home ?? 0;
                        $fixture->away_goals = $item->goals->away ?? 0;
                        $fixture->ht_home_goals = $item->score->halftime->home;
                        $fixture->ht_away_goals = $item->score->halftime->away;
                        $fixture->ft_home_goals = $item->score->fulltime->home;
                        $fixture->ft_away_goals = $item->score->fulltime->away;
                        $fixture->et_home_goals = $item->score->extratime->home;
                        $fixture->et_away_goals = $item->score->extratime->away;
                        $fixture->p_home_goals = $item->score->penalty->home;
                        $fixture->p_away_goals = $item->score->penalty->away;
                        $fixture->is_live = in_array($fixture->status, ['1H', 'HT', '2H', 'ET', 'BT', 'P', 'SUSP', 'INT', 'LIVE']) ? 1 : 0;
                        $fixture->winner_id = $item->teams->home->winner ? $home_team->id : ($item->teams->away->winner ? $away_team->id : null);
                        $fixture->save();
                    }
                }
            }

            JFHFixture::query()
                ->whereNotIn('api_id', $fixturesLiveIds)
                ->whereRaw("starts_at BETWEEN DATE_SUB(NOW(), INTERVAL 150 MINUTE) AND DATE_ADD(NOW(), INTERVAL 15 MINUTE)")
                ->chunk(20, function ($fixtures) {
                    $ids = $fixtures->pluck('api_id')->implode('-');
                    $json = Utils::curl("https://api-football-v1.p.rapidapi.com/v3/fixtures?ids=" . $ids, 'AddFixturesJFHIds');

                    if (isset($json->response)) {
                        foreach ($json->response as $item) {
                            $fixture = JFHFixture::query()
                                ->where(['api_id' => $item->fixture->id])
                                ->first();

                            $home_team = JFHTeam::query()->where(['api_id' => $item->teams->home->id])->first();
                            $away_team = JFHTeam::query()->where(['api_id' => $item->teams->away->id])->first();

                            if ($home_team && $away_team) {
                                $fixture->status = $item->fixture->status->short;
                                $fixture->elapsed = $item->fixture->status->elapsed ?? 0;
                                $fixture->home_goals = $item->goals->home ?? 0;
                                $fixture->away_goals = $item->goals->away ?? 0;
                                $fixture->ht_home_goals = $item->score->halftime->home;
                                $fixture->ht_away_goals = $item->score->halftime->away;
                                $fixture->ft_home_goals = $item->score->fulltime->home;
                                $fixture->ft_away_goals = $item->score->fulltime->away;
                                $fixture->et_home_goals = $item->score->extratime->home;
                                $fixture->et_away_goals = $item->score->extratime->away;
                                $fixture->p_home_goals = $item->score->penalty->home;
                                $fixture->p_away_goals = $item->score->penalty->away;
                                $fixture->is_live = in_array($fixture->status, ['1H', 'HT', '2H', 'ET', 'BT', 'P', 'SUSP', 'INT', 'LIVE']) ? 1 : 0;
                                $fixture->winner_id = $item->teams->home->winner ? $home_team->id : ($item->teams->away->winner ? $away_team->id : null);
                                $fixture->save();
                            }
                        }   
                    }

                    sleep(1);
                });
        }
    }
}
