<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Watson\Validating\ValidatingTrait;

class JFHFact extends Model
{
    use ValidatingTrait;
    use GuiidyModel;
    use SoftDeletes;

    protected $table = "jfh_facts";

    protected $fillable = ['fact', 'description', 'points', 'fixture_id', 'team_id', 'value', 'type', 'is_live', 'still_valid', 'elapsed'];

    protected $rules = [
        'fact' => 'required',
        'description' => 'required',
        'fixture_id' => 'required'
    ];

    public function Fixture() {
        return $this->belongsTo(JFHFixture::class, 'fixture_id');
    }

    public function Team() {
        return $this->belongsTo(JFHTeam::class, 'team_id');
    }
}
