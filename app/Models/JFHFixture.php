<?php

namespace App\Models;

use App\Helpers\Utils;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Watson\Validating\ValidatingTrait;

class JFHFixture extends Model
{
    use ValidatingTrait;
    use GuiidyModel;
    use SoftDeletes;

    protected $table = "jfh_fixtures";

    protected $fillable = [
        'api_id',
        'starts_at', 
        'status', 
        'elapsed', 
        'tvChannel', 
        'link', 
        'home_goals', 
        'away_goals', 
        'ht_home_goals', 
        'ht_away_goals',
        'ft_home_goals',
        'ft_away_goals',
        'et_home_goals',
        'et_away_goals',
        'p_home_goals',
        'p_away_goals',
        'venue_id',
        'league_id',
        'home_team_id',
        'away_team_id',
        'winner_id',
        'season_id',
        'round',
        'is_live',
        'description',
        'video_id',
        'bet_link',
        'is_featured',
        'pre_facts',
        'seconds',
        'ai_overview',
        'ai_price',
        'standings'
    ];

    protected $rules = [
        'api_id' => 'required',
        'starts_at' => 'required',
        'status' => 'required',
        'league_id' => 'required',
        'home_team_id' => 'required',
        'away_team_id' => 'required',
        'season_id' => 'required',
        'round' => 'required'
    ];

    protected $appends = ['date', 'hour', 'small_date', 'day', 'in_play', 'is_featured', 'not_started', 'ai_overview', 'standings'];

    public function getIsFeaturedAttribute() {
        return !empty($this->attributes['is_featured']);
    }

    public function getInPlayAttribute() {
        return in_array($this->attributes['status'], ['LIVE', '1H', 'HT', '2H', 'ET', 'BT', 'P', 'SUSP', 'INT']);
    }

    public function getNotStartedAttribute() {
        return in_array($this->attributes['status'], ['TBD', 'NS', 'AWD', 'WO', 'PST']);
    }

    public function getEndedAttribute() {
        return in_array($this->attributes['status'], ['FT', 'AET', 'PEN', 'PST', 'CANC', 'ABD', 'AWD', 'WO']);
    }

    public function getDateAttribute() {
        $date = Carbon::createFromFormat('Y-m-d H:i:s', $this->attributes['starts_at'], 'UTC');

        $dayMonth = $date->setTimezone('Europe/Lisbon')->format('d/m');
        $hour = $date->setTimezone('Europe/Lisbon')->format('H:i');

        return Utils::getWeekDay($this->attributes['starts_at']) . '. ' . $dayMonth . ' ' . $hour;
    }

    public function getDayAttribute() {
        return Utils::getWeekDay($this->attributes['starts_at'], true) . '. ' . date('d', strtotime($this->attributes['starts_at'])) . ' de ' . Utils::getMonth(date('Y-m-d H:i:s', strtotime($this->attributes['starts_at'])));
    }

    public function League() {
        return $this->belongsTo(JFHLeague::class, 'league_id')->with(['Country']);
    }

    public function HomeTeam() {
        return $this->belongsTo(JFHTeam::class, 'home_team_id')->with(['Country', 'Venue']);
    }

    public function Goals() {
        return $this->hasMany(Goal::class, 'fixture_id')->orderBy('id', 'asc');
    }

    public function Season() {
        return $this->belongsTo(JFHSeason::class, 'season_id');
    }

    public function AwayTeam() {
        return $this->belongsTo(JFHTeam::class, 'away_team_id')->with(['Country', 'Venue']);
    }

    public function Winner() {
        return $this->belongsTo(JFHTeam::class, 'winner_id');
    }

    public function Players() {
        return $this->hasMany(JFHFixturePlayer::class, 'fixture_id')->with(['Player']);
    }

    public function InjuredPlayers() {
        return $this->hasMany(JFHPlayerInjury::class, 'fixture_id')->with(['Player']);
    }

    public function Odds() {
        return $this->hasMany(JFHFixtureOdd::class, 'fixture_id')
            ->with(['Bookmaker', 'Bets'])
            /*
            ->whereHas('Bookmaker', function($q) {
                $q->whereHas('BettingHouse', function($q1) {
                    $q1->where('display', 1);
                });
            })*/;
    }

    public function HomePageOdds() {
        return $this->hasMany(JFHFixtureOdd::class, 'fixture_id')
            ->with(['Bookmaker', 'Bets'])
            ->whereHas('Bookmaker', function($q) {
                $q->where('name', 'Betano');
            });
    }

    public function LiveOdds() {
        return $this->hasMany(JFHLiveOdd::class, 'fixture_id')
            ->with(['Odds']);
    }

    public function AllOdds() {
        return $this->hasMany(JFHFixtureOdd::class, 'fixture_id')
            ->with(['Bookmaker', 'AllBets']);
    }

    public function Venue() {
        return $this->belongsTo(JFHVenue::class, 'venue_id');
    }

    public function Events() {
        return $this->hasMany(JFHFixtureEvent::class, 'fixture_id')->with(['Team', 'Player', 'Assist'])->orderBy('elapsed', 'asc');
    }

    public function H2h() {
        return $this->belongsToMany(JFHFixture::class, 'jfh_fixtures_h2h', 'original_fixture_id', 'fixture_id')->with(['HomeTeam', 'AwayTeam', 'Season', 'League']);
    }

    public function getHourAttribute() {
        $date = Carbon::createFromFormat('Y-m-d H:i:s', $this->attributes['starts_at'], 'UTC');
        return $date->setTimezone('Europe/Lisbon')->format('H:i');
    }

    public function getSmallDateAttribute() {
        $date = Carbon::createFromFormat('Y-m-d H:i:s', $this->attributes['starts_at'], 'UTC');
        return $date->setTimezone('Europe/Lisbon')->format('d/m');
    }

    public function Video() {
        return $this->belongsTo(JFHVideo::class, 'video_id');
    }

    public function Prediction() {
        return $this->hasOne(JFHFixturePredictions::class, 'fixture_id');
    }

    public function Stats() {
        return $this->hasMany(JFHFixtureStat::class, 'fixture_id');
    }

    public function getAiOverviewAttribute() {
        $str = nl2br($this->attributes['ai_overview']);
        $html = preg_replace('/\*\*(.*?)\*\*/', '<b>$1</b>', $str);

        return $html;
    }

    public function getStandingsAttribute() {
        if (empty($this->attributes['standings'])) {
            return null;
        }

        return json_decode($this->attributes['standings']);
    }

    public function setStandingsAttribute($value) {
        $this->attributes['standings'] = json_encode($value);
    }

    public static function boot() {
        parent::boot();

        static::updated(function ($model) {
            if ($model->attributes['status'] && isset($model->getDirty()['status'])) {
                if (in_array($model->attributes['status'], ['FT', 'AET', 'PEN'])) {
                    $prediction = JFHFixturePredictions::query()
                        ->where('fixture_id', $model->attributes['id'])
                        ->first();

                    if ($prediction) {
                        $winnerStr = 'X';

                        if ($model->attributes['winner_id']) {
                            $winnerStr = $model->attributes['winner_id'] == $model->HomeTeam->api_id ? '1' : '2';
                        }

                        $totalGoals = (int) $model->attributes['home_goals'] + (int) $model->attributes['away_goals'];
                        $goals = [
                            'home' => (int) $model->attributes['home_goals'],
                            'away' => (int) $model->attributes['away_goals']
                        ];

                        if ($prediction->_1x2) {
                            if (strlen($prediction->_1x2) == 2) { // double change
                                switch ($prediction->_1x2) {
                                    case '1X': {
                                            $prediction->_correct_1x2 = $winnerStr == '1' || ($goals['home'] == $goals['away']);
                                            break;
                                        }
                                    case 'X2': {
                                            $prediction->_correct_1x2 = $winnerStr == '2' || ($goals['home'] == $goals['away']);
                                            break;
                                        }
                                    default:
                                        break;
                                }
                            } else { // single change
                                $prediction->_correct_1x2 = $prediction->_1x2 == $winnerStr;
                            }

                            $prediction->save();
                        }
                        
                        if ($prediction->_goals) {
                            $prediction->_correct_goals = $prediction->_goals[0] == '-' ? ($totalGoals <= ((int) $prediction->_goals * -1)) : ($totalGoals > (int) $prediction->_goals);
                            $prediction->save();
                        }
                    }
                }
            }
        });
    }
}
