<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Watson\Validating\ValidatingTrait;

class JFHFixtureStat extends Model
{
    use ValidatingTrait;
    use GuiidyModel;
    use SoftDeletes;

    protected $table = "jfh_fixtures_stats";

    protected $fillable = ['type', 'value', 'team_id', 'fixture_id', 'details', 'extra'];

    protected $rules = [
        'type' => 'required',
        'value' => 'required',
        'team_id' => 'required',
        'fixture_id' => 'required'
    ];

    protected $appends = ['details'];

    public function getDetailsAttribute() {
        if (empty($this->attributes['details'])) {
            return null;
        }

        $data = [];
        $details = json_decode($this->attributes['details'], true);

        $min = min(1, min(array_keys($details)));
        $max = max(90, max(array_keys($details)));

        for($i = $max; $i >= $min; $i--) {
            $data[$i] = 0;
        }

        for($i = $min; $i <= $max; $i++) {
            if (isset($details[$i])) {
                $data[$i] = (int) $details[$i];
            }
            else {
                if (!$this->areAllZerosToEnd($details, $max, $i)) {
                    $avg = $this->calculateAvg($i, $details);                
                    $data[$i] = (int) $avg;
                }                
            }
        }

        return $data;
    }

    public function setDetailsAttribute($value) {              
        $this->attributes['details'] = json_encode($value);
    }

    public function Team() {
        return $this->belongsTo(JFHTeam::class, 'team_id');
    }

    public function Fixture() {
        return $this->belongsTo(JFHFixture::class, 'fixture_id');
    }

    private function areAllZerosToEnd($details, $max, $n) {
        $zeros = true;

        for($i = $n; $i <= $max; $i++) {
            if (isset($details[$i]) && $details[$i] != 0) {
                $zeros = false;
                break;
            }
        }

        return $zeros;
    }

    private function calculateAvg($minute, $details) {
        $before = isset($details[$minute - 1]) ? $details[$minute - 1] : null;
        $after = isset($details[$minute + 1]) ? $details[$minute + 1] : null;

        if ($before && $after) {
            return ($before + $after) / 2;
        }

        if ($before && !$after) {
            return $before;
        }

        if (!$before && $after) {
            return $after;
        }

        return false;
    }

    public static function boot()
    {
        parent::boot();

        static::created(function ($model) {
            if ($model->attributes['type'] == 'Corner Kicks') {
                $fixture = JFHFixture::query()
                    ->where('id', $model->attributes['fixture_id'])
                    ->first();

                JFHFixtureEvent::create([
                    'type' => 'Corner',
                    'elapsed' => $fixture->elapsed,
                    'team_id' => $model->attributes['team_id'],
                    'fixture_id' => $fixture->id
                ]);
            }
        });

        static::updated(function ($model) {
            if ($model->attributes['type'] == 'Corner Kicks') {
                if ($model->attributes['value'] && isset($model->getDirty()['value'])) {
                    $fixture = JFHFixture::query()
                        ->where('id', $model->attributes['fixture_id'])
                        ->first();

                    $currentValue = $model->attributes['value'];
                    $oldValue = $model->getOriginal()['value'];

                    for ($i = $oldValue; $i < $currentValue; $i++) {
                        JFHFixtureEvent::create([
                            'type' => 'Corner',
                            'elapsed' => $fixture->elapsed,
                            'team_id' => $model->attributes['team_id'],
                            'fixture_id' => $fixture->id
                        ]);
                    }
                }
            }
        });
    }
}
