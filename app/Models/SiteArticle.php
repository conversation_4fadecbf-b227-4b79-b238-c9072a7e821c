<?php

namespace App\Models;

use Garf\Laravel<PERSON>inger\Pinger;
use Garf\LaravelPinger\PingerFacade;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Watson\Validating\ValidatingTrait;

class SiteArticle extends Model
{
    use ValidatingTrait;
    use GuiidyModel;
    use SoftDeletes;

    protected $table = 'sites_articles';

    protected $fillable = ['title','site_id', 'category_id', 'slug', 'content', 'user_id', 'published_at', 'description', 'is_hero', '_index', 'is_carousel', 'views', 'influencer_id', 'publication_id', 'embed', 'time_to_read', 'tweet', 'verb_id', 'has_video', 'youtube_video_id', '_image', '_category', 'filename', 'keyword', 'parent_id', 'municipality_id'];

    protected $appends = ['is_hero', 'date', 'date_short', 'is_carousel', 'ago', 'views', 'html', 'full_slug', 'has_video', 'toc'];

    protected $rules = [
        'title' => 'required',
        'slug' => 'required',
        'site_id' => 'required',
        'category_id' => 'required',
        'content' => 'required',
        'user_id' => 'required',
        'description' => 'required'
    ];

    public function getContentAttribute() {
        $dom = new \DOMDocument();
        libxml_use_internal_errors(true);
        $dom->loadHTML('<meta http-equiv="Content-Type" content="text/html; charset=utf-8">' . str_replace(PHP_EOL, '', $this->attributes['content']));
        libxml_clear_errors();
        $dom->preserveWhiteSpace = false;

        $h2s = $dom->getElementsByTagName('h2');

        foreach($h2s as $h2) {
            $slug = str_slug(trim($h2->nodeValue));
            $h2->setAttribute('id', $slug);
        }

        return $this->get_inner_html($dom->getElementsByTagName('body')[0]);
    }

    public function getTocAttribute() {
        $dom = new \DOMDocument();
        libxml_use_internal_errors(true);
        $dom->loadHTML('<meta http-equiv="Content-Type" content="text/html; charset=utf-8">' . str_replace(PHP_EOL, '', $this->attributes['content']));
        libxml_clear_errors();
        $dom->preserveWhiteSpace = false;

        $h2s = $dom->getElementsByTagName('h2');

        $toc = [];
        foreach($h2s as $h2) {
            $slug = str_slug(trim($h2->nodeValue));
            $toc[$slug] = trim($h2->nodeValue);
        }

        return $toc;
    }

    private function get_inner_html($node) {
        $innerHTML= '';
        $children = $node->childNodes;
        foreach ($children as $child) {
            $innerHTML .= $child->ownerDocument->saveXML( $child );
        }
    
        return $innerHTML;
    } 

    public function getDateAttribute() {
        return date('j M Y', strtotime($this->attributes['created_at']));
    }

    public function getFullSlugAttribute() {
        return $this->attributes['id'] . '-' . $this->attributes['slug'];
    }

    public function getIsHeroAttribute() {
        return !empty($this->attributes['is_hero']);
    }

    public function getHasVideoAttribute() {
        return !empty($this->attributes['has_video']);
    }

    public function getIsCarouselAttribute() {
        return !empty($this->attributes['is_carousel']);
    }

    public function getDateShortAttribute() {
        return date('d M', strtotime($this->attributes['published_at']));
    }

    public function Tags() {
        return $this->hasMany(Tag::class, 'article_id');
    }

    public function Site() {
        return $this->belongsTo(Site::class);
    }

    public function Parent() {
        return $this->belongsTo(SiteArticle::class, 'parent_id');
    }

    public function Municipality() {
        return $this->belongsTo(Municipality::class);
    }

    public function Category() {
        return $this->belongsTo(Category::class);
    }

    public function Verb() {
        return $this->belongsTo(Verb::class);
    }

    public function User() {
        return $this->belongsTo(User::class);
    }

    public function Comments() {
        return $this->hasMany(Comment::class, 'article_id');
    }

    public function Influencer() {
        return $this->belongsTo(KInfluencer::class, 'influencer_id');
    }

    public function Images() {
        return $this->hasMany(SiteArticleImage::class, 'article_id')->orderBy('id', 'desc');
    }

    public function Reviews() {
        return $this->hasMany(Review::class, 'article_id')->with(['Items', 'Ratings'])->orderBy('_order', 'asc');
    }

    public function Image() {
        return $this->hasOne(SiteArticleImage::class, 'article_id')->orderBy('id', 'desc');
    }

    public function Cover() {
        return $this->hasOne(SiteArticleImage::class, 'article_id')->where('is_primary', 1);
    }

    public function getAgoAttribute() {
        if (isset($this->attributes['published_at'])) {
            return \Carbon\Carbon::createFromTimeStamp(strtotime($this->attributes['published_at']))->diffForHumans();
        }
    }

    public function getViewsAttribute() {
        return SiteArticleStat::query()
            ->where('article_id', $this->attributes['id'])
            ->sum('views');
    }

    public static function boot() {
        parent::boot();

        static::updated(function ($model) {
            $article = SiteArticle::query()
                ->with(['Category', 'Tags'])
                ->where('id', $model->attributes['id'])
                ->first();

            SiteArticle::query()
                ->where('id', $model->attributes['id'])
                ->update(['_category_slug' => $article->Category->slug, '_category_name' => $article->Category->name]);
        });

        static::created(function ($model) {
            $article = SiteArticle::query()
                ->with(['Category', 'Site', 'Verb'])
                ->where('id', $model->attributes['id'])
                ->first();

            SiteArticle::query()
                ->where('id', $model->attributes['id'])
                ->update(['_category_slug' => $article->Category->slug, '_category_name' => $article->Category->name]);

            $url = '';
            switch ($article->Site->url) {
                case 'jogosfutebolhoje.pt': {
                    $url = 'https://jogosfutebolhoje.pt/noticias/' . $article->Category->slug . '/' . $article->full_slug;
                    break;
                }
                case 'nas-redes-sociais.com': {
                    $url = 'https://nas-redes-sociais.com/noticias/' . $article->Category->slug . '/' . $article->full_slug;
                    break;
                }
                case 'commo.pt': {
                    $url = 'https://commo.pt/' . $article->Category->slug . '/como-' . $article->Verb->slug . '/' . $article->full_slug;
                    break;
                }
                default:
                    break;
            }

            if (!empty($url)) {
                PingerFacade::pingAll($article->title, $url);
            }

            if (!empty($model->attributes['publication_id'])) {
                $site = \App\Models\Site::query()->where('url', env('APP_ROLE'))->first();

                \InstagramAPI\Instagram::$allowDangerousWebUsageAtMyOwnRisk = true;

                $ig = new \InstagramAPI\Instagram(false, false, [
                    'storage' => 'mysql',
                    'dbhost' => 'localhost',
                    'dbname' => env('INSTA_DATABASE'),
                    'dbusername' => env('INSTA_USERNAME'),
                    'dbpassword' => env('INSTA_PASSWORD')
                ]);

                try {
                    $ig->login('pedrodavidee', 'Scripting+84_');
                    // $ig->login($site->ig_username, $this->_crypt($site->ig_password, $site->_key, $site->_iv, 'd'));

                    $link = $ig->media->getPermalink($model->attributes['publication_id'])->getPermalink();

                    $result = $ig->request('https://api.instagram.com/oembed/?url=' . urlencode($link));

                    $html = $result->getDecodedResponse()['html'];

                    $site_article = SiteArticle::find($model->attributes['id']);
                    $site_article->_html = $html;
                    $site_article->save();

                } catch (\Exception $e) {

                }
            }
        });
    }

    public function getHtmlAttribute() {
        if ((!empty($this->attributes['publication_id'])) && (empty($this->attributes['embed']))) {
            $site = \App\Models\Site::query()->where('url', env('APP_ROLE'))->first();

            \InstagramAPI\Instagram::$allowDangerousWebUsageAtMyOwnRisk = true;

            $ig = new \InstagramAPI\Instagram(false, false, [
                'storage' => 'mysql',
                'dbhost' => 'localhost',
                'dbname' => env('INSTA_DATABASE'),
                'dbusername' => env('INSTA_USERNAME'),
                'dbpassword' => env('INSTA_PASSWORD')
            ]);

            try {
                $ig->login('pedrodavidee', 'Scripting+84_');
                // $ig->login($site->ig_username, $this->_crypt($site->ig_password, $site->_key, $site->_iv, 'd'));

                $link = $ig->media->getPermalink($this->attributes['publication_id'])->getPermalink();

                $result = $ig->request('https://api.instagram.com/oembed/?url=' . urlencode($link));

                $html = $result->getDecodedResponse()['html'];

                $site_article = SiteArticle::find($this->attributes['id']);
                $site_article->embed = $html;
                $site_article->save();

                return $html;
            } catch (\Exception $e) {

            }
        }

        return false;
    }

    private function _crypt($string, $secret_key, $secret_iv, $action = 'e' ) {
        $output = false;
        $encrypt_method = "AES-256-CBC";
        $key = hash( 'sha256', $secret_key );
        $iv = substr( hash( 'sha256', $secret_iv ), 0, 16 );

        if( $action == 'e' ) {
            $output = base64_encode( openssl_encrypt( $string, $encrypt_method, $key, 0, $iv ) );
        }
        else if( $action == 'd' ){
            $output = openssl_decrypt( base64_decode( $string ), $encrypt_method, $key, 0, $iv );
        }

        return $output;
    }
}
