<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Watson\Validating\ValidatingTrait;

class BettingHouse extends Model
{
    use ValidatingTrait;
    use GuiidyModel;
    use SoftDeletes;

    protected $table = 'av_betting_houses';

    protected $fillable = ['name', 'link', 'short_name', 'slug', 'logo', 'description','cover', 'is_legal', 'stars', 'bonus', 'min_withdrawal', 'min_deposit', 'review', 'display', 'bookmaker_id', '_order', 'review', 'summary', 'include_at_pro', 'bgcolor', 'pro_claim'];

    protected $appends = ['bonus', 'min_withdrawal', 'min_deposit', 'display', 'is_legal', 'toc', 'formatted_review', 'include_at_pro'];

    protected $rules = [
        'name' => 'required',
        'link' => 'required',
        'slug' => 'required',
        'logo' => 'required',
        'description' => 'required',
        'cover' => 'required',
        'stars' => 'required',
        'bonus' => 'required',
        'review' => 'required'
    ];

    public function getIncludeAtProAttribute() {
        return !empty($this->attributes['include_at_pro']);
    }

    public function getBonusAttribute() {
        return str_replace(',','.', $this->attributes['bonus']);
    }

    public function getDisplayAttribute() {
        return !empty($this->attributes['display']);
    }

    public function getIsLegalAttribute() {
        return !empty($this->attributes['is_legal']);
    }

    public function getFormattedReviewAttribute() {
        $dom = new \DOMDocument();
        libxml_use_internal_errors(true);
        $dom->loadHTML('<meta http-equiv="Content-Type" content="text/html; charset=utf-8">' . str_replace(PHP_EOL, '', $this->attributes['review']));
        libxml_clear_errors();
        $dom->preserveWhiteSpace = false;

        $h4s = $dom->getElementsByTagName('h4');        

        foreach($h4s as $h4) {
            $nodeValue = $h4->nodeValue;
            
            $slug = str_slug(trim($nodeValue));
            $h4->setAttribute('id', $slug);
            $h4->setAttribute('class', 'text-lg font-semibold');
        }

        return $this->get_inner_html($dom->getElementsByTagName('body')[0]);
    }

    public function getTocAttribute() {
        $dom = new \DOMDocument();
        libxml_use_internal_errors(true);
        $dom->loadHTML('<meta http-equiv="Content-Type" content="text/html; charset=utf-8">' . str_replace(PHP_EOL, '', $this->attributes['review']));
        libxml_clear_errors();
        $dom->preserveWhiteSpace = false;

        $h4s = $dom->getElementsByTagName('h4');

        $toc = [];
        foreach($h4s as $h4) {
            $nodeValue = $h4->nodeValue;
            $nodeValue = utf8_decode($nodeValue);

            $slug = str_slug(trim($nodeValue));
            $toc[$slug] = trim($nodeValue);
        }

        $toc['sumario'] = 'Sumário';
        $toc['faqs'] = 'Perguntas Frequentes';

        return $toc;
    }

    private function get_inner_html($node) {
        $innerHTML= '';
        $children = $node->childNodes;
        foreach ($children as $child) {
            $innerHTML .= $child->ownerDocument->saveXML( $child );
        }
    
        return $innerHTML;
    } 

    public function getMinWithdrawalAttribute() {
        if (empty($this->attributes['min_withdrawal'])) {
            return 0;
        }

        return str_replace(',','.', $this->attributes['min_withdrawal']);
    }

    public function getMinDepositAttribute() {
        if (empty($this->attributes['min_withdrawal'])) {
            return 0;
        }

        return str_replace(',','.', $this->attributes['min_deposit']);
    }

    public function Features() {
        return $this->belongsToMany(Feature::class, 'av_betting_houses_features')->whereNull('av_betting_houses_features.deleted_at');
    }

    public function PaymentMethods() {
        return $this->belongsToMany(AVPaymentMethod::class, 'av_betting_houses_payment_methods', 'betting_house_id','payment_method_id')->whereNull('av_betting_houses_payment_methods.deleted_at');
    }

    public function Labels() {
        return $this->belongsToMany(Label::class, 'av_betting_houses_labels')->whereNull('av_betting_houses_labels.deleted_at');
    }

    public function Types() {
        return $this->belongsToMany(BettingType::class, 'av_betting_houses_betting_types')->whereNull('av_betting_houses_betting_types.deleted_at');
    }

    public function Apps() {
        return $this->belongsToMany(App::class, 'av_betting_houses_apps')->whereNull('av_betting_houses_apps.deleted_at');
    }

    public function Bookmaker() {
        return $this->belongsTo(JFHBookmaker::class, 'bookmaker_id');
    }

    public function Faqs() {
        return $this->hasMany(FAQ::class, 'betting_house_id');
    }
}
