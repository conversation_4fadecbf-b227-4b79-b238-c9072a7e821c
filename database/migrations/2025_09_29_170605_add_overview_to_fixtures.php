<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddOverviewToFixtures extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('jfh_fixtures', function (Blueprint $table) {
            $table->text('ai_overview')->nullable();
            $table->decimal('ai_price', 8, 4)->nullable();
            $table->text('standings')->nullable();
        });

        Schema::table('jfh_fixtures_stats', function (Blueprint $table) {
            $table->integer('extra')->nullable();
        });

        Schema::table('jfh_facts', function (Blueprint $table) {
            $table->integer('elapsed')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('jfh_facts', function (Blueprint $table) {
            $table->dropColumn(['elapsed']);
        });

        Schema::table('jfh_fixtures_stats', function (Blueprint $table) {
            $table->dropColumn(['extra']);
        });

        Schema::table('jfh_fixtures', function (Blueprint $table) {
            $table->dropColumn(['ai_overview', 'ai_price', 'standings']);
        });
    }
}
